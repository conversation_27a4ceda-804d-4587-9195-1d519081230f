/**
 * 数据集导入任务处理服务
 */

import { PrismaClient } from '@prisma/client';
import { nanoid } from 'nanoid';
import * as XLSX from 'xlsx';
import fs from 'fs';
import { updateTask } from './index';
import { createDataset, updateDataset } from '@/lib/db/datasets';
import { getTags, createTag } from '@/lib/db/tags';
import { getProject } from '@/lib/db/projects';
import { saveChunks } from '@/lib/db/chunks';
import { saveQuestions, updateQuestion } from '@/lib/db/questions';
import LLMClient from '@/lib/llm/core/index';
import { extractJsonFromLLMOutput } from '@/lib/llm/common/util';
import getAddLabelPrompt from '@/lib/llm/prompts/addLabel';
import { getAddLabelSimplePrompt } from '@/lib/llm/prompts/addLabelSimple';
import { parseLLMResponse, extractTagFromText, hasErrorInResponse } from '@/lib/util/llm-response-parser';
import { processInParallel } from '@/lib/util/async';
import { updateFileRecord } from '@/lib/db/file-records';
import { FILE_RECORD } from '@/constant/index';

const prisma = new PrismaClient();

/**
 * 处理数据集导入任务
 * @param {object} task - 任务对象
 * @returns {Promise<void>}
 */
export async function processDatasetImportTask(task) {
  try {
    console.log(`开始处理数据集导入任务: ${task.id}`);

    // 解析任务信息
    let taskNote;
    try {
      taskNote = JSON.parse(task.note);
    } catch (error) {
      throw new Error(`任务信息解析失败: ${error.message}`);
    }

    const { filePath, fileName, fileFormat, fileRecordId, importOptions } = taskNote;

    if (!filePath || !fs.existsSync(filePath)) {
      throw new Error('导入文件不存在或已被删除');
    }

    // 解析导入选项
    const {
      importDomainTag,
      importCot,
      importInstruction,
      autoDetectDomainTag,
      useCustomInstruction,
      customInstruction,
      formatType = 'alpaca',
      customFields = {
        questionField: 'question',
        answerField: 'answer',
        cotField: 'cot',
        includeLabels: false,
        includeChunk: false
      }
    } = importOptions;

    // 处理数据并创建数据集
    const datasets = [];
    const errors = [];

    // 获取项目的领域标签，用于自动识别
    let projectTags = [];
    let domainTreePrompt = '';

    // 如果需要自动识别领域标签，获取项目标签和模型配置
    if (autoDetectDomainTag) {
      try {
        // 获取项目标签
        projectTags = await getTags(task.projectId);
        if (!projectTags || projectTags.length === 0) {
          errors.push('项目没有领域标签，无法自动识别领域标签');
        }

        // 获取项目配置，包括domainTreePrompt
        const projectConfig = await getProject(task.projectId);
        if (projectConfig && projectConfig.domainTreePrompt) {
          domainTreePrompt = projectConfig.domainTreePrompt;
        }
      } catch (error) {
        errors.push(`获取项目标签和配置失败: ${error.message}`);
      }
    }

    // 解析后的问题列表（用于自动识别领域标签）
    const questionsForTagging = [];

    // 根据不同的文件格式处理数据
    if (fileFormat === 'excel') {
      // 读取Excel文件
      const buffer = fs.readFileSync(filePath);
      const workbook = XLSX.read(buffer, { type: 'buffer' });

      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // 将工作表转换为JSON
      const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (rows.length <= 1) {
        throw new Error('Excel文件没有数据');
      }

      // 更新任务总数
      const totalCount = rows.length - 1; // 减去标题行
      await updateTask(task.id, {
        totalCount,
        detail: `待处理数据行数: ${totalCount}`
      });

      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];

        // 确保A列和B列有数据（问题和答案是必填的）
        if (row.length >= 2 && row[0] && row[1]) {
          try {
            // 处理领域标签逻辑
            let questionLabel = '未分类'; // 默认为"未分类"而不是"其他"
            let needAutoDetect = false;

            // 1. 如果只勾选了自动识别标签
            if (autoDetectDomainTag && !importDomainTag) {
              // 只勾选了自动识别，全部进行自动识别
              needAutoDetect = true;
              questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
            }
            // 2. 如果既勾选了领域标签又勾选了自动识别
            else if (autoDetectDomainTag && importDomainTag) {
              // 如果C列为空，则进行自动识别
              if (!row[2] || row[2].trim() === '') {
                needAutoDetect = true;
                questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
              } else {
                // C列有内容，使用导入数据中的标签
                questionLabel = row[2];
                needAutoDetect = false;
              }
            }
            // 3. 如果只勾选了领域标签
            else if (importDomainTag && !autoDetectDomainTag) {
              // 使用导入数据中的标签，如果为空则设为"未分类"
              questionLabel = (row[2] && row[2].trim() !== '') ? row[2] : '未分类';
              needAutoDetect = false;
            }
            // 4. 如果都没有勾选
            else {
              // 设为"未分类"
              questionLabel = '未分类';
              needAutoDetect = false;
            }

            // 处理instruction字段逻辑
            let instructionValue = '请回答以下问题'; // 默认值
            if (useCustomInstruction && customInstruction) {
              // 使用自定义指令
              instructionValue = customInstruction;
            } else if (importInstruction && row.length >= 5 && row[4]) {
              // 使用E列的指令内容
              instructionValue = row[4];
            }

            // 创建虚拟文本块
            const chunkId = nanoid(12);
            const virtualChunk = {
              id: chunkId,
              projectId: task.projectId,
              name: `Excel导入数据-${i}`,
              fileId: 'virtual', // 虚拟文本块标识
              fileName: `Excel导入-${task.id}.xlsx`, // 必填字段
              content: row[0], // 使用问题内容作为文本块内容
              summary: row[0].substring(0, 200), // 添加摘要字段
              size: row[0].length, // 添加大小字段
              createAt: new Date(),
              updateAt: new Date()
            };

            // 保存虚拟文本块
            await saveChunks([virtualChunk]);

            // 创建真实问题记录
            const questionId = nanoid(12);
            const realQuestion = {
              id: questionId,
              projectId: task.projectId,
              chunkId: chunkId,
              question: row[0], // A列: 问题 (必填)
              answer: row[1],   // B列: 答案 (必填)
              instruction: instructionValue, // 根据选项确定指令内容
              model: 'Excel导入',
              label: questionLabel,
              cot: importCot && row.length >= 4 ? row[3] : '', // D列: 思维链 (可选)
              confirmed: true, // 默认为已确认状态
              createdAt: new Date(),
              updatedAt: new Date()
            };

            // 保存真实问题记录
            await saveQuestions(task.projectId, [realQuestion], chunkId);

            const dataset = {
              id: nanoid(12),
              projectId: task.projectId,
              question: row[0], // A列: 问题 (必填)
              answer: row[1],   // B列: 答案 (必填)
              instruction: instructionValue, // 根据选项确定指令内容
              model: 'Excel导入',
              questionLabel: questionLabel,
              cot: importCot && row.length >= 4 ? row[3] : '', // D列: 思维链 (可选)
              chunkName: `Excel导入数据-${i}`,
              chunkContent: row[0],
              questionId: questionId, // 使用真实问题ID
              confirmed: true // 默认为已确认状态
            };

            // 如果需要自动识别，将问题添加到待标记列表
            if (needAutoDetect) {
              questionsForTagging.push({
                id: dataset.id,
                question: dataset.question
              });
            }

            // 创建数据集
            await createDataset(dataset);
            datasets.push(dataset);
          } catch (error) {
            errors.push(`第${i + 1}行导入失败: ${error.message}`);
          }
        } else {
          errors.push(`第${i + 1}行数据不完整，已跳过`);
        }

        // 更新任务进度
        await updateTask(task.id, {
          completedCount: i,
          detail: `已处理: ${i}/${totalCount}, 成功导入: ${datasets.length}`
        });
      }
    } else if (fileFormat === 'json' || fileFormat === 'jsonl') {
      // 读取JSON/JSONL文件
      const text = fs.readFileSync(filePath, 'utf-8');

      let items = [];

      if (fileFormat === 'json') {
        // 解析JSON格式
        try {
          const parsed = JSON.parse(text);
          // 处理数组或单个对象
          items = Array.isArray(parsed) ? parsed : [parsed];
        } catch (error) {
          throw new Error('JSON格式无效');
        }
      } else {
        // 解析JSONL格式（每行一个JSON对象）
        try {
          items = text.split('\n')
            .filter(line => line.trim() !== '')
            .map(line => JSON.parse(line));
        } catch (error) {
          throw new Error('JSONL格式无效');
        }
      }

      if (items.length === 0) {
        throw new Error('文件没有数据');
      }

      // 更新任务总数
      const totalCount = items.length;
      await updateTask(task.id, {
        totalCount,
        detail: `待处理数据项数: ${totalCount}`
      });

      // 处理每个数据项
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        // 根据格式类型进行字段映射
        let questionField, answerField, instructionField, domainTagField, cotField;

        if (formatType === 'alpaca') {
          // 优先使用input字段作为问题内容，如果没有input字段则回退到instruction字段（向后兼容）
          questionField = findFieldValue(item, ['input']);
          if (!questionField) {
            // 向后兼容：如果没有input字段，使用instruction字段
            // 这种情况通常出现在旧版本导出的数据或不标准的Alpaca格式数据中
            questionField = findFieldValue(item, ['instruction']);
            console.warn(`Alpaca数据项缺少input字段，使用instruction字段作为问题内容: ${JSON.stringify(item).substring(0, 100)}...`);
          }

          answerField = findFieldValue(item, ['output']);
          instructionField = findFieldValue(item, ['instruction']) || findFieldValue(item, ['system']);
          domainTagField = findFieldValue(item, ['domainTag', 'label', 'questionLabel', 'tag']);
          cotField = findFieldValue(item, ['cot', 'complexCOT', 'chainOfThought', 'thinking']);
        } else if (formatType === 'sharegpt') {
          // ShareGPT格式映射
          if (item.messages && Array.isArray(item.messages)) {
            // 从messages数组中提取内容
            const userMessage = item.messages.find(msg => msg.role === 'user');
            const assistantMessage = item.messages.find(msg => msg.role === 'assistant');
            const systemMessage = item.messages.find(msg => msg.role === 'system');

            questionField = userMessage?.content;
            answerField = assistantMessage?.content;
            instructionField = systemMessage?.content;
          }
          domainTagField = findFieldValue(item, ['domainTag', 'label', 'questionLabel', 'tag']);
          cotField = findFieldValue(item, ['cot', 'complexCOT', 'chainOfThought', 'thinking']);
        } else if (formatType === 'custom') {
          // 自定义格式映射
          questionField = findFieldValue(item, [customFields.questionField]);
          answerField = findFieldValue(item, [customFields.answerField]);
          cotField = findFieldValue(item, [customFields.cotField]);
          domainTagField = findFieldValue(item, ['domainTag', 'label', 'questionLabel', 'tag']);

          // 处理标签和文本块字段
          if (customFields.includeLabels) {
            domainTagField = domainTagField || findFieldValue(item, ['labels']);
          }
          if (customFields.includeChunk) {
            // 文本块信息可以从chunk字段获取
            const chunkField = findFieldValue(item, ['chunk']);
            if (chunkField) {
              // 可以在这里处理文本块信息
            }
          }
        }

        // 检查必填字段
        if (questionField && answerField) {
          try {
            // 处理领域标签逻辑
            let questionLabel = '未分类'; // 默认为"未分类"而不是"其他"
            let needAutoDetect = false;

            // 1. 如果只勾选了自动识别标签
            if (autoDetectDomainTag && !importDomainTag) {
              // 只勾选了自动识别，全部进行自动识别
              needAutoDetect = true;
              questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
            }
            // 2. 如果既勾选了领域标签又勾选了自动识别
            else if (autoDetectDomainTag && importDomainTag) {
              // 如果标签字段为空，则进行自动识别
              if (!domainTagField || domainTagField.trim() === '') {
                needAutoDetect = true;
                questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
              } else {
                // 标签字段有内容，使用导入数据中的标签
                questionLabel = domainTagField;
                needAutoDetect = false;
              }
            }
            // 3. 如果只勾选了领域标签
            else if (importDomainTag && !autoDetectDomainTag) {
              // 使用导入数据中的标签，如果为空则设为"未分类"
              questionLabel = (domainTagField && domainTagField.trim() !== '') ? domainTagField : '未分类';
              needAutoDetect = false;
            }
            // 4. 如果都没有勾选
            else {
              // 设为"未分类"
              questionLabel = '未分类';
              needAutoDetect = false;
            }

            // 处理instruction字段逻辑
            let instructionValue = '请回答以下问题'; // 默认值
            if (useCustomInstruction && customInstruction) {
              // 使用自定义指令
              instructionValue = customInstruction;
            } else if (instructionField) {
              // 使用导入数据中的指令内容
              instructionValue = instructionField;
            }

            // 创建虚拟文本块
            const chunkId = nanoid(12);
            const virtualChunk = {
              id: chunkId,
              projectId: task.projectId,
              name: `导入数据-${i + 1}`,
              fileId: 'virtual', // 虚拟文本块标识
              fileName: `${fileFormat.toUpperCase()}导入-${task.id}.${fileFormat}`, // 必填字段
              content: questionField, // 使用问题内容作为文本块内容
              summary: questionField.substring(0, 200), // 添加摘要字段
              size: questionField.length, // 添加大小字段
              createAt: new Date(),
              updateAt: new Date()
            };

            // 保存虚拟文本块
            await saveChunks([virtualChunk]);

            // 创建真实问题记录
            const questionId = nanoid(12);
            const realQuestion = {
              id: questionId,
              projectId: task.projectId,
              chunkId: chunkId,
              question: questionField,
              answer: answerField,
              instruction: instructionValue,
              model: `${fileFormat.toUpperCase()}导入`,
              label: questionLabel,
              cot: importCot && cotField ? cotField : '',
              confirmed: true,
              createdAt: new Date(),
              updatedAt: new Date()
            };

            // 保存真实问题记录
            await saveQuestions(task.projectId, [realQuestion], chunkId);

            const dataset = {
              id: nanoid(12),
              projectId: task.projectId,
              question: questionField,
              answer: answerField,
              instruction: instructionValue, // 根据选项确定指令内容
              model: `${fileFormat.toUpperCase()}导入`,
              questionLabel: questionLabel,
              cot: importCot && cotField ? cotField : '',
              chunkName: `导入数据-${i + 1}`,
              chunkContent: questionField,
              questionId: questionId, // 使用真实问题ID
              confirmed: true
            };

            // 如果需要自动识别，将问题添加到待标记列表
            if (needAutoDetect) {
              questionsForTagging.push({
                id: dataset.id,
                question: dataset.question
              });
            }

            // 创建数据集
            await createDataset(dataset);
            datasets.push(dataset);
          } catch (error) {
            errors.push(`第${i + 1}项导入失败: ${error.message}`);
          }
        } else {
          errors.push(`第${i + 1}项缺少必填字段(问题或答案)，已跳过`);
        }

        // 更新任务进度
        await updateTask(task.id, {
          completedCount: i + 1,
          detail: `已处理: ${i + 1}/${totalCount}, 成功导入: ${datasets.length}`
        });
      }
    } else if (fileFormat === 'csv') {
      // 读取CSV文件
      const text = fs.readFileSync(filePath, 'utf-8');

      // 解析CSV
      const rows = text.split('\n').map(row => {
        // 处理CSV中的引号和逗号
        const result = [];
        let inQuotes = false;
        let currentField = '';

        for (let i = 0; i < row.length; i++) {
          const char = row[i];

          if (char === '"') {
            if (inQuotes && i + 1 < row.length && row[i + 1] === '"') {
              // 处理双引号转义
              currentField += '"';
              i++;
            } else {
              // 切换引号状态
              inQuotes = !inQuotes;
            }
          } else if (char === ',' && !inQuotes) {
            // 字段结束
            result.push(currentField);
            currentField = '';
          } else {
            // 添加到当前字段
            currentField += char;
          }
        }

        // 添加最后一个字段
        result.push(currentField);

        return result;
      }).filter(row => row.join('').trim() !== '');

      if (rows.length <= 1) {
        throw new Error('CSV文件没有数据');
      }

      // 获取标题行
      const headers = rows[0];

      // 找到关键列的索引
      const questionIndex = headers.findIndex(h => h.toLowerCase().includes('question') ||
        h.toLowerCase().includes('input'));
      const instructionIndex = headers.findIndex(h => h.toLowerCase().includes('instruction'));
      const answerIndex = headers.findIndex(h => h.toLowerCase().includes('answer') ||
        h.toLowerCase().includes('output') ||
        h.toLowerCase().includes('response'));
      const domainTagIndex = headers.findIndex(h => h.toLowerCase().includes('domain') ||
        h.toLowerCase().includes('tag') ||
        h.toLowerCase().includes('label'));
      const cotIndex = headers.findIndex(h => h.toLowerCase().includes('cot') ||
        h.toLowerCase().includes('chain') ||
        h.toLowerCase().includes('think'));

      // 检查必填列是否存在
      if (questionIndex === -1 || answerIndex === -1) {
        throw new Error('CSV文件缺少必要的问题或答案列');
      }

      // 更新任务总数
      const totalCount = rows.length - 1; // 减去标题行
      await updateTask(task.id, {
        totalCount,
        detail: `待处理数据行数: ${totalCount}`
      });

      // 处理数据行
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];

        if (row.length > Math.max(questionIndex, answerIndex) && row[questionIndex] && row[answerIndex]) {
          try {
            // 处理领域标签逻辑
            let questionLabel = '未分类'; // 默认为"未分类"而不是"其他"
            let needAutoDetect = false;
            const hasDomainTagColumn = domainTagIndex !== -1;
            const hasDomainTagValue = hasDomainTagColumn && row[domainTagIndex] && row[domainTagIndex].trim() !== '';

            // 1. 如果只勾选了自动识别标签
            if (autoDetectDomainTag && !importDomainTag) {
              // 只勾选了自动识别，全部进行自动识别
              needAutoDetect = true;
              questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
            }
            // 2. 如果既勾选了领域标签又勾选了自动识别
            else if (autoDetectDomainTag && importDomainTag) {
              // 如果标签列为空，则进行自动识别
              if (!hasDomainTagValue) {
                needAutoDetect = true;
                questionLabel = '待分类'; // 临时设置为"待分类"，稍后会通过自动识别更新
              } else {
                // 标签列有内容，使用导入数据中的标签
                questionLabel = row[domainTagIndex];
                needAutoDetect = false;
              }
            }
            // 3. 如果只勾选了领域标签
            else if (importDomainTag && !autoDetectDomainTag) {
              // 使用导入数据中的标签，如果为空则设为"未分类"
              questionLabel = hasDomainTagValue ? row[domainTagIndex] : '未分类';
              needAutoDetect = false;
            }
            // 4. 如果都没有勾选
            else {
              // 设为"未分类"
              questionLabel = '未分类';
              needAutoDetect = false;
            }

            // 创建虚拟文本块
            const chunkId = nanoid(12);
            const virtualChunk = {
              id: chunkId,
              projectId: task.projectId,
              name: `CSV导入数据-${i}`,
              fileId: 'virtual', // 虚拟文本块标识
              fileName: `CSV导入-${task.id}.csv`, // 必填字段
              content: row[questionIndex], // 使用问题内容作为文本块内容
              summary: row[questionIndex].substring(0, 200), // 添加摘要字段
              size: row[questionIndex].length, // 添加大小字段
              createAt: new Date(),
              updateAt: new Date()
            };

            // 保存虚拟文本块
            await saveChunks([virtualChunk]);

            // 创建真实问题记录
            const questionId = nanoid(12);
            const instructionValue = (instructionIndex !== -1 && row[instructionIndex]) ? row[instructionIndex] : '请回答以下问题';
            const realQuestion = {
              id: questionId,
              projectId: task.projectId,
              chunkId: chunkId,
              question: row[questionIndex],
              answer: row[answerIndex],
              instruction: instructionValue,
              model: 'CSV导入',
              label: questionLabel,
              cot: (importCot && cotIndex !== -1 && row[cotIndex]) ? row[cotIndex] : '',
              confirmed: true,
              createdAt: new Date(),
              updatedAt: new Date()
            };

            // 保存真实问题记录
            await saveQuestions(task.projectId, [realQuestion], chunkId);

            const dataset = {
              id: nanoid(12),
              projectId: task.projectId,
              question: row[questionIndex],
              answer: row[answerIndex],
              instruction: instructionValue,
              model: 'CSV导入',
              questionLabel: questionLabel,
              cot: (importCot && cotIndex !== -1 && row[cotIndex]) ? row[cotIndex] : '',
              chunkName: `CSV导入数据-${i}`,
              chunkContent: row[questionIndex],
              questionId: questionId, // 使用真实问题ID
              confirmed: true
            };

            // 如果需要自动识别，将问题添加到待标记列表
            if (needAutoDetect) {
              questionsForTagging.push({
                id: dataset.id,
                question: dataset.question
              });
            }

            // 创建数据集
            await createDataset(dataset);
            datasets.push(dataset);
          } catch (error) {
            errors.push(`第${i + 1}行导入失败: ${error.message}`);
          }
        } else {
          errors.push(`第${i + 1}行数据不完整，已跳过`);
        }

        // 更新任务进度
        await updateTask(task.id, {
          completedCount: i,
          detail: `已处理: ${i}/${totalCount}, 成功导入: ${datasets.length}`
        });
      }
    }

    // 如果需要自动识别领域标签且有问题需要标记
    if (autoDetectDomainTag && questionsForTagging.length > 0) {
      try {
        // 检查项目是否有标签
        if (!projectTags || projectTags.length === 0) {
          console.error('自动识别标签失败：项目没有定义领域标签');
          errors.push('项目没有领域标签，无法自动识别领域标签。请先在项目设置中添加领域标签。');
        } else {
          // 批量处理，每次只处理1个问题，避免请求过大
          const batchSize = 1; // 每次只处理1个问题
          let processedCount = 0;
          let autoDetectSuccess = false;

          // 更新任务状态
          await updateTask(task.id, {
            detail: `数据导入完成，开始处理自动识别领域标签，共${questionsForTagging.length}个问题`
          });

          console.log(`开始自动识别领域标签，共${questionsForTagging.length}个问题，项目标签数量: ${projectTags.length}`);

          // 准备标签列表，包含层级关系信息
          const simplifiedTags = [];

          // 递归构建标签树，保留层级关系
          function buildTagTree(tags, parentId = null, path = '') {
            const result = [];

            // 找出当前层级的标签
            const currentLevelTags = tags.filter(tag => tag.parentId === parentId);

            for (const tag of currentLevelTags) {
              // 创建当前标签对象
              const currentPath = path ? `${path} > ${tag.label}` : tag.label;
              const tagObj = {
                id: tag.id,
                name: tag.label,
                parentId: tag.parentId,
                path: currentPath
              };

              // 递归处理子标签
              const children = buildTagTree(tags, tag.id, currentPath);
              if (children.length > 0) {
                tagObj.children = children;
              }

              result.push(tagObj);

              // 同时添加到扁平列表，便于LLM处理
              simplifiedTags.push(tagObj);
            }

            return result;
          }

          // 构建标签树
          let tagTree = buildTagTree(projectTags);

          // 解析模型信息
          let modelConfig;
          try {
            modelConfig = JSON.parse(task.modelInfo);
          } catch (error) {
            throw new Error(`模型信息解析失败: ${error.message}`);
          }

          if (!modelConfig) {
            throw new Error('未找到项目默认模型配置');
          }

          // 使用更小的模型，如果当前是大模型
          // if (modelConfig.modelName && (
          //     modelConfig.modelName.includes('qwen3:8b') ||
          //     modelConfig.modelName.includes('gemma3:12b') ||
          //     modelConfig.modelName.includes('llama3:8b')
          // )) {
          //   console.log(`使用较大模型(${modelConfig.modelName})，自动降级为更小模型`);
          //   modelConfig.modelName = 'qwen3:1.7b';
          // }

          // 创建LLM客户端
          const llmClient = new LLMClient(modelConfig);

          // 处理每个问题
          while (processedCount < questionsForTagging.length) {
            const batch = questionsForTagging.slice(processedCount, processedCount + batchSize);
            processedCount += batch.length;

            try {
              // 更新任务进度
              await updateTask(task.id, {
                detail: `自动识别领域标签进度: ${Math.min(processedCount, questionsForTagging.length)}/${questionsForTagging.length}`
              });

              // 对每个问题单独处理
              for (const question of batch) {
                try {
                  // 检查是否为推理模型
                  const isReasoningModel = modelConfig.modelName && (
                    modelConfig.modelName.includes('o1') ||
                    modelConfig.modelName.includes('reasoning') ||
                    modelConfig.modelName.includes('think')
                  );

                  // 使用简化的提示词
                  const tagsString = JSON.stringify(tagTree, null, 2);
                  const promptContent = getAddLabelSimplePrompt(
                    tagsString,
                    question.question,
                    domainTreePrompt || '',
                    isReasoningModel
                  );

                  // 简化的重试逻辑
                  let retryCount = 0;
                  let success = false;
                  let response;

                  while (retryCount < 3 && !success) {
                    try {
                      console.log(`尝试自动识别领域标签，问题 ${processedCount}/${questionsForTagging.length}，重试次数: ${retryCount}`);

                      // 发送请求，设置较短的超时时间
                      response = await Promise.race([
                        llmClient.getResponse(promptContent),
                        new Promise((_, reject) =>
                          setTimeout(() => reject(new Error('请求超时')), 30000) // 30秒超时
                        )
                      ]);
                      success = true;
                    } catch (retryError) {
                      retryCount++;
                      console.error(`自动识别领域标签请求失败，重试 ${retryCount}/3:`, retryError.message);

                      // 如果是最后一次重试失败，不抛出错误，而是跳过这个问题
                      if (retryCount >= 3) {
                        console.error(`跳过问题"${question.question.substring(0, 20)}..."，无法自动识别标签`);
                        break;
                      }

                      // 等待一段时间再重试
                      await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                  }

                  // 如果成功获取响应
                  if (success && response) {
                    let parentTag = '';
                    let childTag = '';
                    let reasoning = '';

                    // 检查响应是否包含错误信息
                    if (hasErrorInResponse(response)) {
                      console.warn(`自动识别标签失败，使用默认标签: ${response}`);
                      childTag = '未分类';
                    } else {
                      // 尝试解析JSON响应
                      const parseResult = parseLLMResponse(response);

                      if (parseResult.success) {
                        parentTag = parseResult.parentTag;
                        childTag = parseResult.childTag;
                        reasoning = parseResult.reasoning;

                        console.log(`LLM返回结果 - 父标签: ${parentTag}, 子标签: ${childTag}`);
                      } else {
                        console.error('解析LLM JSON响应失败:', parseResult.error);
                        console.log('原始响应:', response);

                        // 如果JSON解析失败，尝试从文本中提取
                        childTag = extractTagFromText(response);
                      }
                    }

                    // 处理标签结果
                    if (childTag) {
                      try {
                        // 查找或创建标签
                        const matchedLabel = await findOrCreateTag(task.projectId, childTag);

                        // 更新数据集标签
                        const updateResult = await updateDatasetLabel(question.id, matchedLabel);
                        if (updateResult) {
                          autoDetectSuccess = true;
                        }

                        // 如果创建了新标签，刷新标签列表
                        if (matchedLabel !== childTag) {
                          try {
                            // 重新获取项目标签
                            projectTags = await getTags(task.projectId);
                            // 重建标签树
                            tagTree = buildTagTree(projectTags);
                          } catch (refreshError) {
                            console.error(`刷新标签列表失败: ${refreshError.message}`);
                          }
                        }
                      } catch (error) {
                        console.error(`更新问题标签失败: ${error.message}`);
                      }
                    } else {
                      console.warn(`无法为问题"${question.question.substring(0, 20)}..."获取有效标签，跳过`);
                    }
                  } else {
                    console.warn(`没有获取到有效响应，无法为问题"${question.question.substring(0, 20)}..."标记标签`);
                  }

                  // 每个问题处理后稍微暂停，避免连续请求导致服务器压力过大
                  await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (questionError) {
                  console.error(`处理问题"${question.question.substring(0, 20)}..."失败:`, questionError);
                  // 继续处理下一个问题，而不是中断整个过程
                }
              }
            } catch (batchError) {
              console.error('处理批量自动标记失败:', batchError);
              errors.push(`处理批量自动标记失败: ${batchError.message}`);
              // 继续处理下一批，而不是中断整个过程
            }
          }

          if (!autoDetectSuccess) {
            errors.push('自动识别领域标签失败。导入的数据已保存，但保留了原始标签。');
          }
        }
      } catch (error) {
        console.error('自动识别领域标签失败:', error);
        errors.push(`自动识别领域标签失败: ${error.message}。导入的数据已保存，但保留了原始标签。`);
      }
    }

    // 清理临时文件
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error(`删除临时文件失败: ${error.message}`);
    }

    // 完成任务
    await updateTask(task.id, {
      status: 1, // 1表示完成
      detail: `成功导入${datasets.length}条数据集`,
      note: JSON.stringify({
        total: datasets.length,
        errors: errors.length > 0 ? errors : []
      }),
      endTime: new Date()
    });

    // 更新文件记录
    if (fileRecordId) {
      try {
        await updateFileRecord(fileRecordId, {
          status: FILE_RECORD.STATUS.SUCCESS,
          recordCount: datasets.length,
          description: `成功导入${datasets.length}条数据集记录`
        });
      } catch (error) {
        console.error('更新文件记录失败:', error);
      }
    }
  } catch (error) {
    console.error(`数据集导入任务失败: ${task.id}`, error);

    // 更新文件记录为失败状态
    const taskNote = JSON.parse(task.note || '{}');
    const { fileRecordId } = taskNote;
    if (fileRecordId) {
      try {
        await updateFileRecord(fileRecordId, {
          status: FILE_RECORD.STATUS.FAILED,
          description: `导入失败: ${error.message}`
        });
      } catch (updateError) {
        console.error('更新文件记录失败状态失败:', updateError);
      }
    }

    await updateTask(task.id, {
      status: 2, // 2表示失败
      detail: `导入失败: ${error.message}`,
      endTime: new Date()
    });
  }
}

/**
 * 在对象中查找可能的字段值
 * @param {Object} item 数据项
 * @param {Array<string>} possibleFields 可能的字段名数组
 * @returns {string|null} 找到的字段值或null
 */
function findFieldValue(item, possibleFields) {
  for (const field of possibleFields) {
    if (item[field] !== undefined && item[field] !== null) {
      return item[field];
    }
  }

  // 检查消息格式（如ShareGPT格式）
  if (item.messages && Array.isArray(item.messages)) {
    for (const message of item.messages) {
      if (message.role === 'user' && possibleFields.includes('question')) {
        return message.content;
      }
      if (message.role === 'assistant' && possibleFields.includes('answer')) {
        return message.content;
      }
    }
  }

  return null;
}

/**
 * 更新数据集的领域标签
 * @param {string} datasetId 数据集ID
 * @param {string} label 领域标签
 */
async function updateDatasetLabel(datasetId, label) {
  try {
    // 确保label是标签名称而不是ID
    if (!label || typeof label !== 'string') {
      console.error(`无效的标签值: ${label}`);
      return false;
    }

    // 首先获取数据集信息以获取关联的问题ID
    const db = new PrismaClient();
    const dataset = await db.datasets.findUnique({
      where: { id: datasetId },
      select: { questionId: true }
    });
    await db.$disconnect();

    // 使用数据库更新函数直接更新数据集
    await updateDataset({
      id: datasetId,
      questionLabel: label
    });

    // 同时更新关联的真实问题记录的标签
    if (dataset && dataset.questionId) {
      try {
        // 先检查问题记录是否存在
        const db2 = new PrismaClient();
        const existingQuestion = await db2.questions.findUnique({
          where: { id: dataset.questionId },
          select: { id: true }
        });
        await db2.$disconnect();

        if (existingQuestion) {
          await updateQuestion(dataset.questionId, {
            label: label
          });
        }
      } catch (questionUpdateError) {
        // 不抛出错误，继续执行，因为数据集标签已经更新成功
      }
    }
    return true;
  } catch (error) {
    // 不抛出错误，避免中断整个导入过程
    // 返回 false 表示更新失败，但不影响其他数据的处理
    return false;
  }
}

/**
 * 查找或创建标签
 * @param {string} projectId 项目ID
 * @param {string} labelName 标签名称
 * @param {string} suggestNewSubclass 可选，LLM建议的新子类名称
 * @returns {Promise<string>} 返回匹配的标签名称
 */
async function findOrCreateTag(projectId, labelName, suggestNewSubclass = null) {
  try {
    // 获取项目的所有标签
    const allTags = await getTags(projectId);

    // 去除前后空格
    const trimmedLabelName = labelName.trim();

    // 1. 首先进行精确匹配（完全相同的标签名称）
    const exactMatch = allTags.find(tag => tag.label === trimmedLabelName);
    if (exactMatch) {
      console.log(`找到完全匹配的标签: ${exactMatch.label}`);
      return exactMatch.label;
    }

    // 2. 如果没有精确匹配，进行大小写不敏感的匹配
    const caseInsensitiveMatch = allTags.find(tag =>
      tag.label.toLowerCase() === trimmedLabelName.toLowerCase()
    );
    if (caseInsensitiveMatch) {
      console.log(`找到大小写不敏感匹配的标签: ${caseInsensitiveMatch.label}`);
      return caseInsensitiveMatch.label;
    }

    // 3. 检查是否是子标签格式（如"1.1 社交互动"）
    const childTagMatch = trimmedLabelName.match(/^(\d+)\.(\d+)\s+(.+)$/);
    if (childTagMatch) {
      const parentNum = childTagMatch[1];
      const childNum = childTagMatch[2];
      const childName = childTagMatch[3];

      console.log(`检测到子标签格式: ${trimmedLabelName}，父标签序号: ${parentNum}，子标签序号: ${childNum}，标签名: ${childName}`);

      // 查找对应的父标签
      const parentTagName = await findOrCreateParentTag(projectId, parentNum);
      if (parentTagName) {
        console.log(`找到现有父标签: ${parentTagName}`);
        // 创建或查找子标签
        const childTagName = await findOrCreateChildTag(projectId, parentTagName, childName, childNum);
        console.log(`创建新的子标签: ${childTagName}，父标签: ${parentTagName}`);
        return childTagName;
      }
    }

    // 4. 如果都没有匹配，才创建新标签
    console.log(`未找到匹配的标签，将创建新标签: ${trimmedLabelName}`);

    // 检查是否是一级标签（父级标签）
    function isParentTag(name) {
      return name.includes('领域') || /^[一二三四五六七八九十]+、/.test(name);
    }

    // 如果有LLM建议的新子类，优先处理
    if (suggestNewSubclass && typeof suggestNewSubclass === 'string' && suggestNewSubclass.trim() !== '') {
      // 先检查建议的子类是否已经存在
      const suggestedSubclassName = suggestNewSubclass.trim();
      const existingSubclass = allTags.find(tag => tag.label === suggestedSubclassName);
      if (existingSubclass) {
        console.log(`建议的子类标签已存在: ${existingSubclass.label}`);
        return existingSubclass.label;
      }

      // 查找父标签（精确匹配）
      const parentTag = allTags.find(tag => tag.label === trimmedLabelName);

      if (parentTag) {
        // 创建新的子类标签
        try {
          const newTag = await createTag(projectId, suggestedSubclassName, parentTag.id);
          console.log(`已创建新的子类标签: ${suggestedSubclassName}，父类: ${parentTag.label}`);
          return suggestedSubclassName;
        } catch (error) {
          console.error(`创建子类标签失败: ${error.message}`);
          // 如果创建失败，返回父类标签
          return parentTag.label;
        }
      } else {
        // 父标签不存在，需要先创建父标签和子标签
        return await createNewDomainTag(projectId, trimmedLabelName, suggestedSubclassName);
      }
    }

    // 如果没有找到任何匹配的标签，创建新标签
    if (isParentTag(trimmedLabelName)) {
      // 如果是领域标签，创建领域标签和子标签
      return await createNewDomainTag(projectId, trimmedLabelName, suggestNewSubclass);
    } else {
      // 如果是普通标签，直接创建
      try {
        const newTag = await createTag(projectId, trimmedLabelName, null);
        console.log(`已创建新标签: ${trimmedLabelName}`);
        return trimmedLabelName;
      } catch (error) {
        console.error(`创建标签失败: ${error.message}`);
        return '未分类';
      }
    }
  } catch (error) {
    console.error(`查找或创建标签失败: ${error.message}`);
    // 出错时返回原始标签名称
    return labelName;
  }
}

/**
 * 检查是否已存在类似的子标签
 * @param {Array} tags 所有标签
 * @param {string} parentId 父标签ID
 * @param {string} childName 子标签名称
 * @param {string} cleanName 清理后的子标签名称（不含前缀）
 * @returns {object|null} 找到的标签或null
 */
function findSimilarChildTag(tags, parentId, childName, cleanName) {
  // 首先尝试精确匹配
  const exactMatch = tags.find(tag =>
    tag.parentId === parentId && tag.label === childName
  );

  if (exactMatch) {
    return exactMatch;
  }

  // 如果没有精确匹配，尝试匹配清理后的名称部分
  const cleanNameMatch = tags.find(tag => {
    if (tag.parentId !== parentId) return false;

    // 清理标签名称，去除前缀数字和标点
    const tagCleanName = tag.label.replace(/^[一二三四五六七八九十]+、\s*/, '')
      .replace(/^\d+[、\.]\d*\s*/, '')
      .replace(/^\d+\s*/, '')
      .trim();

    return tagCleanName === cleanName;
  });

  return cleanNameMatch || null;
}

/**
 * 查找或创建父标签
 * @param {string} projectId 项目ID
 * @param {string} parentNum 父标签序号
 * @returns {Promise<string>} 返回父标签名称
 */
async function findOrCreateParentTag(projectId, parentNum) {
  try {
    const allTags = await getTags(projectId);

    // 查找现有的父标签（一级标签）
    const existingParent = allTags.find(tag => {
      if (tag.parentId) return false; // 只查找一级标签

      // 匹配中文数字格式
      const chineseMatch = tag.label.match(/^([一二三四五六七八九十]+)、/);
      if (chineseMatch) {
        const chineseToArabic = {
          '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
          '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
        };
        return chineseToArabic[chineseMatch[1]] === parentNum;
      }

      // 匹配阿拉伯数字格式
      const arabicMatch = tag.label.match(/^(\d+)[、\.]/);
      if (arabicMatch) {
        return arabicMatch[1] === parentNum;
      }

      return false;
    });

    if (existingParent) {
      console.log(`找到现有父标签: ${existingParent.label}`);
      return existingParent.label;
    }

    // 如果没有找到，创建新的父标签
    const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    const parentIndex = parseInt(parentNum) - 1;
    let parentPrefix;

    if (parentIndex >= 0 && parentIndex < chineseNumbers.length) {
      parentPrefix = `${chineseNumbers[parentIndex]}、`;
    } else {
      parentPrefix = `${parentNum}、`;
    }

    // 根据序号确定领域名称
    const domainNames = {
      '1': '社交互动',
      '2': '日常生活',
      '3': '学习发展',
      '4': '职业工作',
      '5': '休闲娱乐',
      '6': '健康医疗',
      '7': '特殊需求'
    };

    const domainName = domainNames[parentNum] || '其他';
    const newParentName = `${parentPrefix}${domainName}`;

    // 创建父标签
    await createTag(projectId, newParentName, null);
    console.log(`创建新的父标签: ${newParentName}`);

    return newParentName;
  } catch (error) {
    console.error(`查找或创建父标签失败: ${error.message}`);
    return '其他';
  }
}

/**
 * 查找或创建子标签
 * @param {string} projectId 项目ID
 * @param {string} parentTagName 父标签名称
 * @param {string} childTagName 子标签名称
 * @param {string} childNum 子标签序号
 * @returns {Promise<string>} 返回子标签名称
 */
async function findOrCreateChildTag(projectId, parentTagName, childTagName, childNum) {
  try {
    const allTags = await getTags(projectId);

    // 找到父标签
    const parentTag = allTags.find(tag => tag.label === parentTagName && !tag.parentId);
    if (!parentTag) {
      console.error(`未找到父标签: ${parentTagName}`);
      return childTagName;
    }

    // 清理子标签名称，去除可能的数字前缀（如"1.1 社交互动"中的"1.1 "）
    const cleanChildName = childTagName.replace(/^\d+\.\d+\s+/, '').trim();

    // 查找现有的子标签
    const existingChild = allTags.find(tag => {
      if (tag.parentId !== parentTag.id) return false;

      // 精确匹配标签名
      if (tag.label === cleanChildName) return true;

      // 匹配带序号的格式
      const match = tag.label.match(/^\d+\.\d+\s+(.+)$/);
      if (match && match[1] === cleanChildName) return true;

      return false;
    });

    if (existingChild) {
      console.log(`找到现有子标签: ${existingChild.label}`);
      return existingChild.label;
    }

    // 获取父标签的阿拉伯数字序号
    const parentNum = parentTagName.match(/^([一二三四五六七八九十]+|\d+)/)?.[1];
    let parentNumArabic = parentNum;

    // 转换中文数字为阿拉伯数字
    if (parentNum) {
      const chineseToArabic = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
      };
      parentNumArabic = chineseToArabic[parentNum] || parentNum;
    }

    // 查找该父标签下已存在的子标签，确定下一个可用的子序号
    const existingChildTags = allTags.filter(tag => tag.parentId === parentTag.id);
    let maxChildNum = 0;

    existingChildTags.forEach(tag => {
      const match = tag.label.match(/^\d+\.(\d+)\s+/);
      if (match) {
        const childSeqNum = parseInt(match[1], 10);
        if (!isNaN(childSeqNum) && childSeqNum > maxChildNum) {
          maxChildNum = childSeqNum;
        }
      }
    });

    // 使用下一个可用的子序号
    const nextChildNum = maxChildNum + 1;
    const newChildName = `${parentNumArabic}.${nextChildNum} ${cleanChildName}`;

    // 创建子标签
    await createTag(projectId, newChildName, parentTag.id);
    console.log(`创建新的子标签: ${newChildName}，父标签: ${parentTagName}`);

    return newChildName;
  } catch (error) {
    console.error(`查找或创建子标签失败: ${error.message}`);
    return childTagName;
  }
}

/**
 * 创建新的领域标签
 * @param {string} projectId 项目ID
 * @param {string} tagName 领域标签名称
 * @param {string} subclassName 可选，子类标签名称
 * @returns {Promise<string>} 返回创建的标签名称
 */
async function createNewDomainTag(projectId, tagName, subclassName = null) {
  try {
    // 获取项目的所有标签
    const allTags = await getTags(projectId);

    // 确定新领域标签的序号
    let domainNumber = 0;

    // 查找现有的一级标签，确定下一个序号
    for (const tag of allTags) {
      if (!tag.parentId) { // 只处理一级标签
        // 提取中文数字或阿拉伯数字
        const chinesePrefix = tag.label.match(/^([一二三四五六七八九十]+)、/);
        const arabicPrefix = tag.label.match(/^(\d+)[、\.]/);

        if (chinesePrefix) {
          // 将中文数字转换为阿拉伯数字
          const chineseToArabic = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
          };
          const num = chineseToArabic[chinesePrefix[1]] || 0;
          if (num > domainNumber) {
            domainNumber = num;
          }
        } else if (arabicPrefix) {
          const num = parseInt(arabicPrefix[1], 10);
          if (!isNaN(num) && num > domainNumber) {
            domainNumber = num;
          }
        }
      }
    }

    // 创建新的领域标签
    domainNumber++;

    // 使用中文数字格式
    const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    let domainPrefix;
    if (domainNumber <= 10) {
      domainPrefix = `${chineseNumbers[domainNumber - 1]}、`;
    } else {
      domainPrefix = `${domainNumber}、`;
    }

    // 处理标签名称，标准化格式
    function standardizeTagName(name) {
      if (!name) return '';

      // 去除前缀数字和标点
      let cleaned = name.replace(/^[一二三四五六七八九十]+、\s*/, '');
      cleaned = cleaned.replace(/^\d+[、\.]\d*\s*/, '');
      cleaned = cleaned.replace(/^\d+\s*/, '');

      // 去除可能的领域后缀
      cleaned = cleaned.replace(/领域$/, '');

      return cleaned.trim();
    }

    // 标准化领域标签名称
    const normalizedTagName = standardizeTagName(tagName);

    // 一级标签格式：一、社交互动领域
    const newDomainName = `${domainPrefix}${normalizedTagName}领域`;

    // 检查是否已存在相同名称的一级标签
    const existingDomainTag = allTags.find(tag => tag.label === newDomainName && !tag.parentId);

    let domainTagId;
    if (existingDomainTag) {
      console.log(`已存在领域标签: ${newDomainName}，直接使用`);
      domainTagId = existingDomainTag.id;
    } else {
      const newDomain = await createTag(projectId, newDomainName, null);
      console.log(`已创建新的领域标签: ${newDomainName}`);
      domainTagId = newDomain.id;
    }

    // 处理子类标签名称
    let normalizedSubclass;
    if (subclassName) {
      normalizedSubclass = standardizeTagName(subclassName);
    } else {
      normalizedSubclass = normalizedTagName;
    }

    // 二级标签格式：1.1 日常社交（使用小数点，前面的数字与一级标签序号一致）
    const defaultSubName = `${domainNumber}.1 ${normalizedSubclass}`;

    // 检查是否已存在相同或类似的子标签
    const existingSubTag = findSimilarChildTag(
      allTags,
      domainTagId,
      defaultSubName,
      normalizedSubclass
    );

    if (existingSubTag) {
      console.log(`已存在子标签: ${existingSubTag.label}，直接使用`);
      return existingSubTag.label;
    }

    const newSubTag = await createTag(projectId, defaultSubName, domainTagId);
    console.log(`已创建默认子类标签: ${defaultSubName}`);

    // 返回最低层级的标签值
    return defaultSubName;
  } catch (error) {
    console.error(`创建新领域标签失败: ${error.message}`);
    throw error;
  }
}
