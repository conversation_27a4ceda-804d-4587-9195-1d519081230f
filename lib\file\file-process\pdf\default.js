import pdf2md from '@opendocsg/pdf2md';
import { getProjectRoot } from '@/lib/db/base';
import { downloadDocument } from '@/lib/oss-simple';
import { getUploadFileInfoById } from '@/lib/db/upload-files';
import fs from 'fs';
import path from 'path';

export async function defaultProcessing(projectId, fileName, options = {}) {
  console.log('executing default pdf conversion strategy......');

  try {
    // 从options中获取fileId
    const { fileId } = options;
    if (!fileId) {
      throw new Error('fileId is required for OSS file processing');
    }

    // 从OSS下载PDF文件
    console.log(`从OSS下载PDF文件: ${fileName}, fileId: ${fileId}`);
    const fileResult = await downloadDocument(fileId);
    
    // 创建临时文件路径
    const tempDir = path.join(process.cwd(), 'temp');
    const tempFilePath = path.join(tempDir, fileName);
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 将PDF内容写入临时文件
    fs.writeFileSync(tempFilePath, Buffer.from(fileResult.content, 'binary'));

    // 转换后文件名
    const convertName = fileName.replace(/\.([^.]*)$/, '') + '.md';

    try {
      // 读取临时PDF文件
      const pdfBuffer = fs.readFileSync(tempFilePath);
      
      // 转换PDF
      const text = await pdf2md(pdfBuffer);
      
      // 获取项目路径用于保存转换后的文件
      const projectRoot = await getProjectRoot();
      const projectPath = path.join(projectRoot, projectId);
      const outputFile = path.join(projectPath, 'files', convertName);
      
      console.log(`Writing to ${outputFile}...`);
      fs.writeFileSync(path.resolve(outputFile), text);
      console.log('pdf conversion completed!');

      // 清理临时文件
      fs.unlinkSync(tempFilePath);

      // 返回转换后的文件名
      return { success: true, fileName: convertName };
    } catch (err) {
      console.error('pdf conversion failed:', err);
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      throw err;
    }
  } catch (error) {
    console.error('defaultProcessing error:', error);
    throw error;
  }
}

export default {
  defaultProcessing
};
