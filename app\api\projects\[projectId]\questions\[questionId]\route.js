import { NextResponse } from 'next/server';
import { deleteQuestion, updateQuestion } from '@/lib/db/questions';

// 更新单个问题
export async function PUT(request, { params }) {
  try {
    const { projectId, questionId } = params;

    // 验证参数
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    if (!questionId) {
      return NextResponse.json({ error: 'Question ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const { question, tagIds } = body;

    // 验证必要字段
    if (!question || !question.trim()) {
      return NextResponse.json({ error: 'Question content is required' }, { status: 400 });
    }

    if (!tagIds || !Array.isArray(tagIds) || tagIds.length === 0) {
      return NextResponse.json({ error: 'At least one tag is required' }, { status: 400 });
    }

    // 更新问题
    const updatedQuestion = await updateQuestion(questionId, {
      question: question.trim(),
      tagIds: tagIds
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Update successful',
      question: updatedQuestion 
    });
  } catch (error) {
    console.error('Update failed:', String(error));
    return NextResponse.json({ error: error.message || 'Update failed' }, { status: 500 });
  }
}

// 删除单个问题
export async function DELETE(request, { params }) {
  try {
    const { projectId, questionId } = params;

    // 验证参数
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    if (!questionId) {
      return NextResponse.json({ error: 'Question ID is required' }, { status: 400 });
    }

    // 删除问题
    await deleteQuestion(questionId);

    return NextResponse.json({ success: true, message: 'Delete successful' });
  } catch (error) {
    console.error('Delete failed:', String(error));
    return NextResponse.json({ error: error.message || 'Delete failed' }, { status: 500 });
  }
}
