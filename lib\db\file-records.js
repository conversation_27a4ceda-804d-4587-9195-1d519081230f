'use server';

import { db } from '@/lib/db/index';
import { FILE_RECORD } from '@/constant/index';

/**
 * 创建文件记录
 * @param {Object} fileRecordData 文件记录数据
 * @returns {Promise<Object>} 创建的文件记录
 */
export async function createFileRecord(fileRecordData) {
  try {
    return await db.fileRecords.create({
      data: fileRecordData
    });
  } catch (error) {
    console.error('创建文件记录失败:', error);
    throw error;
  }
}

/**
 * 更新文件记录
 * @param {string} id 文件记录ID
 * @param {Object} updateData 更新数据
 * @returns {Promise<Object>} 更新后的文件记录
 */
export async function updateFileRecord(id, updateData) {
  try {
    return await db.fileRecords.update({
      where: { id },
      data: updateData
    });
  } catch (error) {
    console.error('更新文件记录失败:', error);
    throw error;
  }
}

/**
 * 检查文件是否已存在（基于MD5）
 * @param {string} projectId 项目ID
 * @param {string} md5Hash MD5哈希值
 * @param {string} operationType 操作类型
 * @returns {Promise<Object|null>} 已存在的文件记录
 */
export async function checkFileRecordByMD5(projectId, md5Hash, operationType) {
  try {
    return await db.fileRecords.findFirst({
      where: {
        projectId,
        md5Hash,
        operationType,
        status: { not: FILE_RECORD.STATUS.DELETED }
      }
    });
  } catch (error) {
    console.error('检查文件记录失败:', error);
    throw error;
  }
}
