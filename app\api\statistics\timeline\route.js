import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    const projectId = request.nextUrl.searchParams.get('projectId');
    const months = parseInt(request.nextUrl.searchParams.get('months')) || 6;

    // 计算时间范围（最近N个月）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - months + 1);
    startDate.setDate(1);
    startDate.setHours(0, 0, 0, 0);

    // 生成月份标签
    const monthLabels = [];
    const monthKeys = [];
    for (let i = 0; i < months; i++) {
      const date = new Date(startDate);
      date.setMonth(startDate.getMonth() + i);
      monthLabels.push(`${date.getMonth() + 1}月`);
      monthKeys.push(`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`);
    }

    // 构建查询条件
    const whereClause = projectId ? { projectId } : {};

    // 1. 查询数据集时间序列数据
    const datasets = await prisma.datasets.findMany({
      where: {
        ...whereClause,
        createAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        createAt: true,
        confirmed: true,
        questionLabel: true
      }
    });

    // 2. 查询问题时间序列数据
    const questions = await prisma.questions.findMany({
      where: {
        ...whereClause,
        createAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        createAt: true
      }
    });

    // 3. 按月统计数据
    const monthlyStats = {};
    monthKeys.forEach(key => {
      monthlyStats[key] = {
        totalDatasets: 0,
        confirmedDatasets: 0,
        categorizedDatasets: 0,
        totalQuestions: 0
      };
    });

    // 统计数据集
    datasets.forEach(dataset => {
      const monthKey = `${dataset.createAt.getFullYear()}-${String(dataset.createAt.getMonth() + 1).padStart(2, '0')}`;
      if (monthlyStats[monthKey]) {
        monthlyStats[monthKey].totalDatasets++;

        if (dataset.confirmed === true || dataset.confirmed === 1) {
          monthlyStats[monthKey].confirmedDatasets++;
        }

        if (dataset.questionLabel &&
          dataset.questionLabel !== '待分类' &&
          dataset.questionLabel !== '未分类') {
          monthlyStats[monthKey].categorizedDatasets++;
        }
      }
    });

    // 统计问题
    questions.forEach(question => {
      const monthKey = `${question.createAt.getFullYear()}-${String(question.createAt.getMonth() + 1).padStart(2, '0')}`;
      if (monthlyStats[monthKey]) {
        monthlyStats[monthKey].totalQuestions++;
      }
    });

    // 4. 转换为累积数据（显示总量趋势）
    let cumulativeQuestions = 0;
    let cumulativeDatasets = 0;
    let cumulativeCategorized = 0;
    let cumulativeConfirmed = 0;

    const timeSeriesData = {
      xData: monthLabels,
      data1: [], // 问题总数趋势（累积）
      data2: [], // 数据集趋势（累积）
      data3: [], // 已归类趋势（累积）
      data4: [], // 已确认趋势（累积）
      monthlyData: {
        data1: [], // 问题月增量
        data2: [], // 数据集月增量
        data3: [], // 已归类月增量
        data4: [], // 已确认月增量
      }
    };

    monthKeys.forEach(key => {
      const stats = monthlyStats[key];

      // 累积数据
      cumulativeQuestions += stats.totalQuestions;
      cumulativeDatasets += stats.totalDatasets;
      cumulativeCategorized += stats.categorizedDatasets;
      cumulativeConfirmed += stats.confirmedDatasets;

      timeSeriesData.data1.push(cumulativeQuestions);
      timeSeriesData.data2.push(cumulativeDatasets);
      timeSeriesData.data3.push(cumulativeCategorized);
      timeSeriesData.data4.push(cumulativeConfirmed);

      // 月增量数据
      timeSeriesData.monthlyData.data1.push(stats.totalQuestions);
      timeSeriesData.monthlyData.data2.push(stats.totalDatasets);
      timeSeriesData.monthlyData.data3.push(stats.categorizedDatasets);
      timeSeriesData.monthlyData.data4.push(stats.confirmedDatasets);
    });

    return NextResponse.json(timeSeriesData);

  } catch (error) {
    console.error('获取时间序列统计数据失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
