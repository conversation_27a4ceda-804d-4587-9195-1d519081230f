/**
 * Incremental domain tree revision prompt template (English version)
 * Used to incrementally adjust the domain tree based on added/deleted literature content
 */
function getLabelReviseEnPrompt({ text, existingTags, deletedContent, newContent, globalPrompt, domainTreePrompt }) {
  const prompt = `


## Existing Domain Tree
${JSON.stringify(existingTags, null, 2)}

## Original Table of Contents
${text}

## Deleted Content
${deletedContent || 'None'}

## New Content
${newContent || 'None'}

Please analyze the above information and revise the existing domain tree structure according to the following principles:
1. Maintain the overall structure of the domain tree, avoiding large-scale reconstruction
2. For domain tags related to deleted content:
   - If a tag is only related to the deleted content and no supporting content can be found in the existing literature, remove the tag
   - If a tag is also related to other retained content, keep the tag
3. For newly added content:
   - If new content can be classified into existing tags, prioritize using existing tags
   - If new content introduces new domains or concepts not present in the existing tag system, create new tags
4. Each tag must correspond to actual content in the table of contents, do not create empty tags without corresponding content support
5. Ensure that the revised domain tree still has a good hierarchical structure with reasonable parent-child relationships between tags

## Constraints
1. The number of primary domain labels should be between 5 and 10.
2. The number of secondary domain labels should be between 1 and 10.
3. Maximum of two classification levels.
4. The classification must be relevant to the original catalog content.
5. The output must conform to the specified JSON format.
6. The names of the labels should not exceed 6 characters.
7. Label format standards:
   - Primary labels: Must use the "二、Social Interaction" format, with Chinese numerals followed by a comma
   - Secondary labels: Must use the "2.1 Daily Social" format, where the number before the dot represents the primary label number, and the number after the dot represents the sequence

Output the complete revised domain tree structure using the JSON format below:

\`\`\`json
[
  {
    "label": "一、Social Interaction",
    "child": [
      {"label": "1.1 Daily Social"},
      {"label": "1.2 Emotional Exchange"}
    ]
  },
  {
    "label": "二、Education",
    "child": [
      {"label": "2.1 Subject Knowledge"}
    ]
  }
]
\`\`\`

Ensure that your answer only contains the domain tree in JSON format without any explanatory text.`;

  return prompt;
}

module.exports = getLabelReviseEnPrompt;
