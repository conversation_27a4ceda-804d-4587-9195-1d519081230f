/**
 * 全局常量
 */

export const FILE = {
  MAX_FILE_SIZE: 50 * 1024 * 1024 // 50MB in bytes
};

export const TASK = {
  STATUS: {
    PROCESSING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELED: 3
  },
  TYPE: {
    FILE_PROCESSING: 'file-processing',
    QUESTION_GENERATION: 'question-generation',
    ANSWER_GENERATION: 'answer-generation',
    DATA_DISTILLATION: 'data-distillation',
    DATASET_IMPORT: 'dataset-import'
  }
};

/**
 * 文件记录相关常量
 */
export const FILE_RECORD = {
  // 操作类型
  OPERATION_TYPE: {
    IMPORT: 'import',      // 导入数据集
    EXPORT: 'export',      // 导出数据集
    UPLOAD: 'upload'       // 上传文献
  },
  // 文件格式
  FORMAT: {
    EXCEL: 'excel',
    JSON: 'json',
    JSONL: 'jsonl',
    CSV: 'csv',
    PDF: 'pdf',
    DOCX: 'docx',
    MARKDOWN: 'markdown',
    TXT: 'txt'
  },
  // 文件状态
  STATUS: {
    PROCESSING: 0,  // 处理中
    SUCCESS: 1,     // 成功
    FAILED: 2,      // 失败
    DELETED: 3      // 已删除
  },
  // MIME类型映射
  MIME_TYPES: {
    'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'json': 'application/json',
    'jsonl': 'application/jsonl',
    'csv': 'text/csv',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'markdown': 'text/markdown',
    'txt': 'text/plain'
  }
};

/**
 * 默认领域标签体系
 */
export const DEFAULT_DOMAIN_TAGS = [
  {
    label: '一、社交互动',
    child: [
      { label: '1.1 日常社交' },
      { label: '1.2 情感交流' },
      { label: '1.3 人际关系' },
      { label: '1.4 社会交往' },
      { label: '1.5 社交礼仪' }
    ]
  },
  {
    label: '二、日常生活',
    child: [
      { label: '2.1 居家事务' },
      { label: '2.2 消费购物' },
      { label: '2.3 饮食事务' },
      { label: '2.4 服饰穿戴' },
      { label: '2.5 交通出行' },
      { label: '2.6 时间管理' }
    ]
  },
  {
    label: '三、学习发展',
    child: [
      { label: '3.1 正式教育' },
      { label: '3.2 高等教育' },
      { label: '3.3 职业培训' },
      { label: '3.4 自主学习' },
      { label: '3.5 知识获取' }
    ]
  },
  {
    label: '四、职业工作',
    child: [
      { label: '4.1 求职就业' },
      { label: '4.2 职场协作' },
      { label: '4.3 商务活动' },
      { label: '4.4 职业技能' },
      { label: '4.5 工作管理' },
      { label: '4.6 职业规划' }
    ]
  },
  {
    label: '五、休闲娱乐',
    child: [
      { label: '5.1 文化体验' },
      { label: '5.2 旅行探索' },
      { label: '5.3 运动健康' },
      { label: '5.4 游戏娱乐' },
      { label: '5.5 艺术爱好' },
      { label: '5.6 社交娱乐' }
    ]
  },
  {
    label: '六、健康医疗',
    child: [
      { label: '6.1 日常保健' },
      { label: '6.2 疾病预防' },
      { label: '6.3 医疗服务' },
      { label: '6.4 康复护理' },
      { label: '6.5 心理健康' }
    ]
  },
  {
    label: '七、家庭事务',
    child: [
      { label: '7.1 婚姻关系' },
      { label: '7.2 育儿教养' },
      { label: '7.3 养老赡养' },
      { label: '7.4 家庭财务' },
      { label: '7.5 家庭规划' }
    ]
  },
  {
    label: '八、金融财务',
    child: [
      { label: '8.1 日常理财' },
      { label: '8.2 投资管理' },
      { label: '8.3 借贷信贷' },
      { label: '8.4 保险规划' },
      { label: '8.5 税务处理' }
    ]
  },
  {
    label: '九、法律事务',
    child: [
      { label: '9.1 民事纠纷' },
      { label: '9.2 权益保障' },
      { label: '9.3 法律文书' },
      { label: '9.4 法律咨询' },
      { label: '9.5 公共事务' }
    ]
  },
  {
    label: '十、科技应用',
    child: [
      { label: '10.1 数码设备' },
      { label: '10.2 网络应用' },
      { label: '10.3 软件工具' },
      { label: '10.4 信息安全' },
      { label: '10.5 新兴技术' }
    ]
  },
  {
    label: '十一、特殊需求',
    child: [
      { label: '11.1 跨文化沟通' },
      { label: '11.2 无障碍支持' },
      { label: '11.3 应急处理' },
      { label: '11.4 特殊场景' }
    ]
  },
  {
    label: '其他',
    child: []
  }
];
