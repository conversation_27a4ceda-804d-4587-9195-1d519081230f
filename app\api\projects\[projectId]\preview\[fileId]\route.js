import { NextResponse } from 'next/server';
import { downloadDocument } from '@/lib/oss-simple';

// 获取文件内容
export async function GET(request, { params }) {
  try {
    const { projectId, fileId } = params;

    // 验证参数
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID cannot be empty' }, { status: 400 });
    }

    if (!fileId) {
      return NextResponse.json({ error: 'File ID cannot be empty' }, { status: 400 });
    }

    // 使用OSS下载服务获取文件内容
    const fileContentResult = await downloadDocument(fileId);

    return NextResponse.json({
      fileId: fileContentResult.fileId,
      fileName: fileContentResult.fileName,
      content: fileContentResult.content,
      size: fileContentResult.size
    });
  } catch (error) {
    console.error('Failed to get text block content:', String(error));
    return NextResponse.json({ error: error.message || 'Failed to get text block content' }, { status: 500 });
  }
}
