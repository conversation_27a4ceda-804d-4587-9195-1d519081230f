import { NextResponse } from 'next/server';
import {
  deleteDataset,
  getDatasetsById,
  updateDataset
} from '@/lib/db/datasets';
import datasetService from '@/lib/services/datasets';
import { db } from '@/lib/db';

// 优化思维链函数已移至服务层

/**
 * 生成数据集（为单个问题生成答案）
 */
export async function POST(request, { params }) {
  try {
    const { projectId } = params;
    const { questionId, model, language } = await request.json();

    // 使用数据集生成服务
    const result = await datasetService.generateDatasetForQuestion(projectId, questionId, {
      model,
      language
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to generate dataset:', String(error));
    return NextResponse.json(
      {
        error: error.message || 'Failed to generate dataset'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取项目的所有数据集
 */
export async function GET(request, { params }) {
  try {
    const { projectId } = params;
    const page = parseInt(request.nextUrl.searchParams.get('page')) || 1;
    const size = parseInt(request.nextUrl.searchParams.get('size')) || 10;
    const status = request.nextUrl.searchParams.get('status') || 'all';
    const input = request.nextUrl.searchParams.get('input') || '';
    const field = request.nextUrl.searchParams.get('field') || 'question';
    const hasCot = request.nextUrl.searchParams.get('hasCot') || 'all';
    const tag = request.nextUrl.searchParams.get('tag') || ''; // 获取标签过滤参数
    const selectedAll = request.nextUrl.searchParams.get('selectedAll') === '1'; // 是否获取所有符合条件的数据

    const skip = (page - 1) * size;
    const take = selectedAll ? undefined : size; // 如果需要获取所有数据，不设置take限制

    // 构建查询条件
    const where = {
      projectId
    };

    // 根据确认状态过滤
    if (status === 'confirmed') {
      where.confirmed = true;
    } else if (status === 'unconfirmed') {
      where.confirmed = false;
    }

    // 根据思维链状态过滤
    if (hasCot === 'yes') {
      where.cot = { not: null };
    } else if (hasCot === 'no') {
      where.cot = null;
    }

    // 根据输入内容过滤
    if (input) {
      if (field === 'question') {
        where.question = { contains: input };
      } else if (field === 'answer') {
        where.answer = { contains: input };
      } else if (field === 'cot') {
        where.cot = { contains: input };
      } else if (field === 'label') {
        where.questionLabel = { contains: input };
      }
    }

    // 根据标签过滤
    if (tag) {
      // 检查是否包含子标签信息（格式为JSON字符串）
      try {
        const tagData = JSON.parse(tag);
        if (tagData.isParentTag && tagData.childTags && tagData.childTags.length > 0) {
          // 如果是父标签，构建OR查询条件，包含父标签和所有子标签
          where.OR = [
            { questionLabel: { contains: tagData.label } }, // 父标签
            ...tagData.childTags.map(childTag => ({
              questionLabel: { contains: childTag }
            })) // 子标签
          ];
        } else {
          // 普通标签过滤
          where.questionLabel = { contains: tag };
        }
      } catch (e) {
        // 如果解析失败，按普通标签处理
        where.questionLabel = { contains: tag };
      }
    }

    // 查询数据
    const [data, total, confirmed] = await Promise.all([
      db.datasets.findMany({
        where,
        skip,
        take,
        orderBy: {
          createAt: 'desc'
        }
      }),
      db.datasets.count({ where }),
      db.datasets.count({
        where: {
          ...where,
          confirmed: true
        }
      })
    ]);

    // 如果是获取所有数据（用于全选），则只返回ID列表
    if (selectedAll) {
      return NextResponse.json(data);
    }

    return NextResponse.json({
      data,
      page,
      size,
      total,
      confirmed,
      totalPages: Math.ceil(total / size)
    });
  } catch (error) {
    console.error('获取数据集列表失败:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * 删除数据集
 */
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const datasetId = searchParams.get('id');
    if (!datasetId) {
      return NextResponse.json(
        {
          error: 'Dataset ID cannot be empty'
        },
        { status: 400 }
      );
    }

    await deleteDataset(datasetId);

    return NextResponse.json({
      success: true,
      message: 'Dataset deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete dataset:', error);
    return NextResponse.json(
      {
        error: error.message || 'Failed to delete dataset'
      },
      { status: 500 }
    );
  }
}

/**
 * 编辑数据集
 */
export async function PATCH(request) {
  try {
    const { searchParams } = new URL(request.url);
    const datasetId = searchParams.get('id');
    const { answer, cot, confirmed, question, questionLabel } = await request.json();
    if (!datasetId) {
      return NextResponse.json(
        {
          error: 'Dataset ID cannot be empty'
        },
        { status: 400 }
      );
    }
    // 获取所有数据集
    let dataset = await getDatasetsById(datasetId);
    if (!dataset) {
      return NextResponse.json(
        {
          error: 'Dataset does not exist'
        },
        { status: 404 }
      );
    }
    let data = { id: datasetId };
    if (confirmed !== undefined) data.confirmed = confirmed;
    if (answer !== undefined) data.answer = answer;
    if (cot !== undefined) data.cot = cot;
    if (question !== undefined) data.question = question;
    if (questionLabel !== undefined) data.questionLabel = questionLabel;

    // 保存更新后的数据集列表
    await updateDataset(data);

    return NextResponse.json({
      success: true,
      message: 'Dataset updated successfully',
      dataset: dataset
    });
  } catch (error) {
    console.error('Failed to update dataset:', String(error));
    return NextResponse.json(
      {
        error: error.message || 'Failed to update dataset'
      },
      { status: 500 }
    );
  }
}
