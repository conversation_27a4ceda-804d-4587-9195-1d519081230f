'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

/**
 * 标签编辑对话框组件
 * @param {Object} props
 * @param {boolean} props.open - 对话框是否打开
 * @param {Function} props.onClose - 关闭对话框的回调
 * @param {Object} props.tag - 要编辑的标签对象
 * @param {string} props.projectId - 项目ID
 * @param {Function} props.onSuccess - 编辑成功的回调
 */
export default function TagEditDialog({ open, onClose, tag, projectId, onSuccess }) {
  const { t } = useTranslation();
  const [tagName, setTagName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 当标签变化时，更新输入框的值
  useEffect(() => {
    if (tag) {
      setTagName(tag.label || '');
    }
  }, [tag]);

  // 重置状态
  const resetState = () => {
    setTagName('');
    setError('');
    setLoading(false);
  };

  // 处理对话框关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 处理标签编辑
  const handleEdit = async () => {
    if (!tagName.trim()) {
      setError(t('distill.tagNameRequired'));
      return;
    }

    if (tagName.trim() === tag?.label) {
      // 如果名称没有变化，直接关闭
      handleClose();
      return;
    }

    try {
      setLoading(true);
      setError('');

      // 调用更新标签API
      const response = await axios.put(`/api/projects/${projectId}/tags`, {
        tags: {
          id: tag.id,
          label: tagName.trim(),
          parentId: tag.parentId
        }
      });

      if (response.data) {
        // 编辑成功
        if (onSuccess) {
          onSuccess(response.data.tags);
        }
        handleClose();
      }
    } catch (error) {
      console.error('编辑标签失败:', error);
      setError(error.response?.data?.error || t('distill.editTagError'));
    } finally {
      setLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleEdit();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('textSplit.editTag')}</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          <TextField
            autoFocus
            fullWidth
            label={t('domain.tagName')}
            value={tagName}
            onChange={(e) => setTagName(e.target.value)}
            onKeyPress={handleKeyPress}
            error={!!error}
            helperText={error}
            disabled={loading}
            placeholder={t('domain.inputEditTagName')}
          />
          {tag && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {t('domain.originalTagName')}: {tag.label}
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleEdit}
          variant="contained"
          disabled={loading || !tagName.trim()}
        >
          {loading ? t('common.saving') : t('common.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
