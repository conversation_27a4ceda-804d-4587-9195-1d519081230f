import * as echarts from 'echarts';

// 备用简化地图数据（网络请求失败时使用）
const fallbackGeoJSON = {
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": { "name": "中国" },
      "geometry": {
        "type": "Polygon",
        "coordinates": [[
          [73.66, 53.56], [134.77, 53.56], [134.77, 18.16], [73.66, 18.16], [73.66, 53.56]
        ]]
      }
    }
  ]
};

// 缓存已加载的地图数据
let chinaMapLoaded = false;

// 异步加载完整的亚洲地图数据
const loadAsiaMap = async () => {
  if (chinaMapLoaded) {
    return true; // 已经加载过了
  }

  try {
    console.log('正在加载完整的亚洲地图数据...');
    const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const worldGeoJSON = await response.json();

    // 验证数据格式
    if (!worldGeoJSON || !worldGeoJSON.features || !Array.isArray(worldGeoJSON.features)) {
      throw new Error('Invalid GeoJSON format');
    }

    // 过滤出亚洲国家的数据
    const asiaCountries = ['China', 'Japan', 'South Korea', 'North Korea', 'Mongolia', 'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Myanmar', 'Thailand', 'Laos', 'Vietnam', 'Cambodia', 'Malaysia', 'Singapore', 'Indonesia', 'Philippines', 'Brunei', 'East Timor', 'Afghanistan', 'Iran', 'Iraq', 'Syria', 'Lebanon', 'Jordan', 'Israel', 'Palestine', 'Saudi Arabia', 'Yemen', 'Oman', 'United Arab Emirates', 'Qatar', 'Bahrain', 'Kuwait', 'Turkey', 'Cyprus', 'Georgia', 'Armenia', 'Azerbaijan', 'Kazakhstan', 'Kyrgyzstan', 'Tajikistan', 'Turkmenistan', 'Uzbekistan', 'Russia'];

    const asiaGeoJSON = {
      type: 'FeatureCollection',
      features: worldGeoJSON.features.filter(feature =>
        asiaCountries.includes(feature.properties.NAME) ||
        asiaCountries.includes(feature.properties.name) ||
        asiaCountries.includes(feature.properties.NAME_EN)
      )
    };

    // 注册亚洲地图数据
    echarts.registerMap('asia', asiaGeoJSON);
    chinaMapLoaded = true;
    console.log('亚洲地图数据加载成功，包含', asiaGeoJSON.features.length, '个国家');
    return true;
  } catch (error) {
    console.warn('加载完整地图数据失败，使用备用数据:', error.message);
    // 使用备用简化数据
    echarts.registerMap('asia', fallbackGeoJSON);
    chinaMapLoaded = true;
    return false;
  }
};
//  地图数据 - 亚洲主要城市
const mapData = {
  citys: []
};

// 东盟十国列表（ASEAN）
const aseanCountries = [
  'Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia',
  'Myanmar', 'Philippines', 'Singapore', 'Thailand', 'Vietnam'
];

// 动态生成区域配置
const generateRegionsConfig = (highlightCountries = aseanCountries, countryData = {}, selectedCountry = null, showLabels = true) => {
  const regions = [];

  // 所有亚洲国家列表
  const allAsiaCountries = [
    'China', 'Japan', 'South Korea', 'North Korea', 'Mongolia', 'India', 'Pakistan',
    'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Myanmar', 'Thailand', 'Laos',
    'Vietnam', 'Cambodia', 'Malaysia', 'Singapore', 'Indonesia', 'Philippines',
    'Brunei', 'East Timor', 'Afghanistan', 'Iran', 'Iraq', 'Syria', 'Lebanon',
    'Jordan', 'Israel', 'Palestine', 'Saudi Arabia', 'Yemen', 'Oman',
    'United Arab Emirates', 'Qatar', 'Bahrain', 'Kuwait', 'Turkey', 'Cyprus',
    'Georgia', 'Armenia', 'Azerbaijan', 'Kazakhstan', 'Kyrgyzstan', 'Tajikistan',
    'Turkmenistan', 'Uzbekistan', 'Russia'
  ];

  allAsiaCountries.forEach(country => {
    const isHighlighted = highlightCountries.includes(country);
    const data = countryData[country] || {};
    const hiddenCountries = ['China', 'Japan', 'South Korea', 'India'];
    const isHidden = hiddenCountries.includes(country);
    const isSelected = selectedCountry === country;

    if (isHighlighted) {
      // 高亮国家 - 显示标签和特殊样式
      // 只有东盟十国才显示标签
      const isAseanCountry = aseanCountries.includes(country);

      regions.push({
        name: country,
        itemStyle: {
          normal: {
            areaColor: isSelected ? 'rgba(102,105,240,.8)' : (data.color || 'rgba(145, 147, 240, 0.1)'),
            borderColor: isSelected ? '#FFD700' : (data.borderColor || 'rgba(255,209,163,.6)'),
            borderWidth: isSelected ? 2 : (data.borderWidth || 1),
            shadowBlur: isSelected ? 10 : 0,
            shadowColor: isSelected ? 'rgba(255,215,0,.8)' : 'transparent'
          },
          emphasis: {
            areaColor: data.emphasisColor || 'rgba(102,105,240,.7)',
            borderColor: data.emphasisBorderColor || '#FFD1A3',
            borderWidth: 2
          }
        },
        label: {
          show: isAseanCountry, // 只有东盟十国显示标签
          color: '#fff',
          fontSize: 11,
          fontWeight: 'bold',
          textBorderColor: 'rgba(0,0,0,0.8)',
          textBorderWidth: 1,
          textShadowColor: 'rgba(0,0,0,0.8)',
          textShadowBlur: 3,
          textShadowOffsetX: 1,
          textShadowOffsetY: 1,
          formatter: function (params) {
            // 英文到中文的名称映射
            const nameMap = {
              'Brunei': '文莱',
              'Cambodia': '柬埔寨',
              'Indonesia': '印尼',
              'Laos': '老挝',
              'Malaysia': '马来西亚',
              'Myanmar': '缅甸',
              'Philippines': '菲律宾',
              'Singapore': '新加坡',
              'Thailand': '泰国',
              'Vietnam': '越南'
            };
            return nameMap[params.name] || params.name;
          },
          emphasis: {
            show: isAseanCountry, // 悬停时也只有东盟十国显示标签
            color: '#FFD700',
            fontSize: 13,
            fontWeight: 'bold',
            textBorderColor: 'rgba(0,0,0,0.9)',
            textBorderWidth: 2,
            textShadowColor: 'rgba(0,0,0,0.9)',
            textShadowBlur: 5
          }
        }
      });
    } else if (isHidden) {
      // 隐藏的国家 - 完全透明
      regions.push({
        name: country,
        silent: true,
        itemStyle: {
          normal: {
            areaColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0
          },
          emphasis: {
            areaColor: 'transparent',
            borderColor: 'transparent'
          }
        }
      });
    } else {
      // 其他国家 - 背景显示
      regions.push({
        name: country,
        silent: true,
        itemStyle: {
          normal: {
            areaColor: 'rgba(15,15,15,.01)',
            borderColor: 'rgba(35,35,35, .05)',
            borderWidth: 0.3
          },
          emphasis: {
            areaColor: 'rgba(15,15,15,.01)',
            borderColor: 'rgba(35,35,35, .05)'
          }
        }
      });
    }
  });

  return regions;
};

export const mapOptions = async (params, selectedCountry = null, globalStatistics = {}) => {
  // 确保地图数据已加载
  await loadAsiaMap();

  // 从参数中获取高亮国家和国家数据
  const highlightCountries = params?.highlightCountries || aseanCountries;
  const countryData = params?.countryData || {};
  const showAseanOnly = params?.showAseanOnly !== false; // 默认只显示东盟十国
  const showLabels = params?.showLabels !== false; // 默认显示标签

  // 国家名称映射
  const nameMap = {
    'Brunei': '文莱',
    'Cambodia': '柬埔寨',
    'Indonesia': '印度尼西亚',
    'Laos': '老挝',
    'Malaysia': '马来西亚',
    'Myanmar': '缅甸',
    'Philippines': '菲律宾',
    'Singapore': '新加坡',
    'Thailand': '泰国',
    'Vietnam': '越南'
  };

  // 根据选中的国家设置标题
  const titleText = selectedCountry ? nameMap[selectedCountry] || selectedCountry : '';

  return {
    title: {
      show: false,
      text: titleText,
      subtext: params?.subtitle || '',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 16
      },
      subtextStyle: {
        color: '#999',
        fontSize: 12
      }
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: function (params) {
        // 英文到中文的名称映射
        const nameMap = {
          'Brunei': '文莱',
          'Cambodia': '柬埔寨',
          'Indonesia': '印度尼西亚',
          'Laos': '老挝',
          'Malaysia': '马来西亚',
          'Myanmar': '缅甸',
          'Philippines': '菲律宾',
          'Singapore': '新加坡',
          'Thailand': '泰国',
          'Vietnam': '越南'
        };

        // 中文到英文的反向映射
        const reverseNameMap = {
          '文莱': 'Brunei',
          '柬埔寨': 'Cambodia',
          '印度尼西亚': 'Indonesia',
          '老挝': 'Laos',
          '马来西亚': 'Malaysia',
          '缅甸': 'Myanmar',
          '菲律宾': 'Philippines',
          '新加坡': 'Singapore',
          '泰国': 'Thailand',
          '越南': 'Vietnam'
        };

        // 获取英文国家名称（用于数据查找）
        let englishName = reverseNameMap[params.name] || params.name;

        // 获取中文显示名称
        const displayName = nameMap[englishName] || params.name;

        // 使用真实统计数据，没有数据时显示0
        const data = globalStatistics[englishName] || {};

        return `
        <div style="padding: 8px;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #FFD700;">${displayName}</div>
          <div style="margin-bottom: 4px;">📊 数据集数量: <span style="color: #4FC3F7;">${data.datasets || 0}</span></div>
          <div style="margin-bottom: 4px;">❓ 问题数量: <span style="color: #81C784;">${(data.questions || 0).toLocaleString()}</span></div>
          <div style="margin-bottom: 4px;">🚀 项目数量: <span style="color: #FFB74D;">${data.projects || 0}</span></div>
          ${data.lastUpdated ? `<div style="margin-top: 6px; font-size: 12px; color: #999;">更新时间: ${new Date(data.lastUpdated).toLocaleString()}</div>` : ''}
        </div>
      `;
      },
      backgroundColor: 'rgba(0,0,0,0.9)',
      borderColor: '#FFD1A3',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      extraCssText: 'border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);'
    },
    legend: {
      show: false,
    },
    geo: {
      map: 'asia',
      label: {
        show: true,
        color: '#fff',
        fontSize: 11,
        fontWeight: 'bold',
        textBorderColor: 'rgba(0,0,0,0.8)',
        textBorderWidth: 1,
        textShadowColor: 'rgba(0,0,0,0.8)',
        textShadowBlur: 3,
        textShadowOffsetX: 1,
        textShadowOffsetY: 1,
        formatter: function (params) {
          // 只显示东盟十国的标签
          const aseanCountries = [
            'Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia',
            'Myanmar', 'Philippines', 'Singapore', 'Thailand', 'Vietnam'
          ];

          // 如果不是东盟国家，返回空字符串（不显示标签）
          if (!aseanCountries.includes(params.name)) {
            return '';
          }

          // 英文到中文的名称映射
          const nameMap = {
            'Brunei': '文莱',
            'Cambodia': '柬埔寨',
            'Indonesia': '印尼',
            'Laos': '老挝',
            'Malaysia': '马来西亚',
            'Myanmar': '缅甸',
            'Philippines': '菲律宾',
            'Singapore': '新加坡',
            'Thailand': '泰国',
            'Vietnam': '越南'
          };
          return nameMap[params.name] || params.name;
        },
        emphasis: {
          show: true,
          color: '#FFD700',
          fontSize: 13,
          fontWeight: 'bold',
          textBorderColor: 'rgba(0,0,0,0.9)',
          textBorderWidth: 2,
          textShadowColor: 'rgba(0,0,0,0.9)',
          textShadowBlur: 5,
          formatter: function (params) {
            // 悬停时也只显示东盟十国的标签
            const aseanCountries = [
              'Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia',
              'Myanmar', 'Philippines', 'Singapore', 'Thailand', 'Vietnam'
            ];

            // 如果不是东盟国家，返回空字符串（不显示标签）
            if (!aseanCountries.includes(params.name)) {
              return '';
            }

            // 英文到中文的名称映射
            const nameMap = {
              'Brunei': '文莱',
              'Cambodia': '柬埔寨',
              'Indonesia': '印尼',
              'Laos': '老挝',
              'Malaysia': '马来西亚',
              'Myanmar': '缅甸',
              'Philippines': '菲律宾',
              'Singapore': '新加坡',
              'Thailand': '泰国',
              'Vietnam': '越南'
            };
            return nameMap[params.name] || params.name;
          }
        },
      },
      // 动态配置区域样式
      regions: generateRegionsConfig(showAseanOnly ? aseanCountries : highlightCountries, countryData, selectedCountry),
      center: [118.0, 9.0], // 设置东盟十国区域中心点
      zoom: 6.5, // 设置默认缩放级别
      aspectScale: 0.75, // 设置地图长宽比
      // 启用地图缩放和平移功能
      roam: true, // 开启鼠标缩放和平移漫游
      scaleLimit: {
        min: 5, // 最小缩放比例
        max: 15    // 最大缩放比例
      },
      itemStyle: {
        normal: {
          borderColor: 'rgba(255,209,163, .5)', //区域边框颜色
          areaColor: 'rgba(73,86,166,.2)', //默认区域颜色
          borderWidth: 0.5, //区域边框宽度
          shadowBlur: 5,
          shadowColor: 'rgba(107,91,237,.7)',
        },
        emphasis: {
          borderColor: '#FFD1A3',
          areaColor: 'rgba(102,105,240,.4)',
          borderWidth: 1,
          shadowBlur: 5,
          shadowColor: 'rgba(135,138,255,.5)',
        },
      },
    },
    series: [
      {
        type: 'map',
        map: 'asia',
        geoIndex: 0,
        data: [
          { name: 'Brunei', value: 1 },
          { name: 'Cambodia', value: 1 },
          { name: 'Indonesia', value: 1 },
          { name: 'Laos', value: 1 },
          { name: 'Malaysia', value: 1 },
          { name: 'Myanmar', value: 1 },
          { name: 'Philippines', value: 1 },
          { name: 'Singapore', value: 1 },
          { name: 'Thailand', value: 1 },
          { name: 'Vietnam', value: 1 }
        ],
        showLegendSymbol: false
      }
    ],
  };
};
