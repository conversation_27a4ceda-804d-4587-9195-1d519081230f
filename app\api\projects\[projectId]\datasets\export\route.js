import { NextResponse } from 'next/server';
import { getDatasets } from '@/lib/db/datasets';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

/**
 * 获取导出数据集
 */
export async function GET(request, { params }) {
  try {
    const { projectId } = params;
    // 验证项目ID
    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    // 获取查询参数
    const confirmedParam = request.nextUrl.searchParams.get('confirmed');
    let confirmed = undefined;
    if (confirmedParam === 'true') confirmed = true;
    if (confirmedParam === 'false') confirmed = false;

    // 获取数据集
    let datasets = await getDatasets(projectId, confirmed);
    return NextResponse.json(datasets);
  } catch (error) {
    console.error('获取数据集失败:', String(error));
    return NextResponse.json(
      {
        error: error.message || '获取数据集失败'
      },
      { status: 500 }
    );
  }
}
