import React, { PureComponent } from 'react';
import Chart from '../../../utils/chart';
import { OfflinePortalOptions } from './options';

class OfflinePortal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      renderer: 'canvas',
    };
  }

  render() {
    const { renderer } = this.state;
    const { offlinePortalData } = this.props;
    return (
      <div
        style={{
          width: '5.375rem',
          height: '2.875rem',
        }}>
        {offlinePortalData && offlinePortalData.xData && offlinePortalData.data1 ? (
          <Chart
            renderer={renderer}
            option={OfflinePortalOptions(offlinePortalData)}
          />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#BCDCFF',
            fontSize: '14px'
          }}>
            暂无数据
          </div>
        )}
      </div>
    );
  }
}

export default OfflinePortal;
