// 国家列表常量 - 东南亚国家
export const COUNTRIES = [
  { code: 'BN', name: '文莱', nameEn: 'Brunei' },
  { code: 'KH', name: '柬埔寨', nameEn: 'Cambodia' },
  { code: 'ID', name: '印度尼西亚', nameEn: 'Indonesia' },
  { code: 'LA', name: '老挝', nameEn: 'Laos' },
  { code: 'MY', name: '马来西亚', nameEn: 'Malaysia' },
  { code: 'MM', name: '缅甸', nameEn: 'Myanmar' },
  { code: 'PH', name: '菲律宾', nameEn: 'Philippines' },
  { code: 'SG', name: '新加坡', nameEn: 'Singapore' },
  { code: 'TH', name: '泰国', nameEn: 'Thailand' },
  { code: 'VN', name: '越南', nameEn: 'Vietnam' }
];

// 根据语言获取国家名称
export function getCountryName(countryCode, language = 'zh') {
  const country = COUNTRIES.find(c => c.code === countryCode);
  if (!country) return countryCode;
  return language === 'en' ? country.nameEn : country.name;
}

// 获取所有国家选项（用于下拉菜单）
export function getCountryOptions(language = 'zh') {
  return COUNTRIES.map(country => ({
    value: country.code,
    label: language === 'en' ? country.nameEn : country.name
  }));
}