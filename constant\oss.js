﻿/**
 * OSS存储配置常量
 * 只保留必要的配置项
 */

// 支持的文件扩展名 - 只支持Markdown和PDF
export const SUPPORTED_EXTENSIONS = ['.md', '.pdf'];

// MIME类型映射（只支持Markdown和PDF）
export const MIME_TYPES = {
  '.md': 'text/markdown',
  '.pdf': 'application/pdf'
};

/**
 * 生成OSS文件路径 - 极简版
 * @param {string} projectId - 项目ID
 * @param {string} fileName - 文件名
 * @returns {string} OSS路径
 */
export function generateOSSPath(projectId, fileName) {
  const env = process.env.OSS_ENVIRONMENT || 'dev';
  return `${env}/projects/${projectId}/files/${fileName}`;
}

/**
 * 获取文件MIME类型
 * @param {string} fileName - 文件名
 * @returns {string} MIME类型
 */
export function getMimeType(fileName) {
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return MIME_TYPES[ext] || 'application/octet-stream';
}

/**
 * 检查文件类型是否支持
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否支持
 */
export function isSupportedFileType(fileName) {
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return SUPPORTED_EXTENSIONS.includes(ext);
}
