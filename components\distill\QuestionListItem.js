'use client';

import { useState } from 'react';
import {
  ListItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { useTranslation } from 'react-i18next';

/**
 * 问题列表项组件
 * @param {Object} props
 * @param {Object} props.question - 问题对象
 * @param {number} props.level - 缩进级别
 * @param {Function} props.onDelete - 删除问题的回调
 * @param {Function} props.onEdit - 编辑问题的回调
 * @param {Function} props.onGenerateDataset - 生成数据集的回调
 * @param {boolean} props.processing - 是否正在处理
 */
export default function QuestionListItem({ question, level, onDelete, onEdit, onGenerateDataset, processing = false }) {
  const { t } = useTranslation();

  return (
    <ListItem
      sx={{
        pl: (level + 1) * 2,
        py: 0.75,
        borderLeft: '1px dashed rgba(0, 0, 0, 0.1)',
        ml: 2,
        borderBottom: '1px solid',
        borderColor: 'divider',
        '&:hover': {
          bgcolor: 'action.hover'
        }
      }}
      secondaryAction={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Tooltip title={t('datasets.generateDataset')}>
            <IconButton size="small" color="primary" onClick={e => onGenerateDataset(e)} disabled={processing}>
              {processing ? <CircularProgress size={16} /> : <AutoFixHighIcon fontSize="small" />}
            </IconButton>
          </Tooltip>
          <Tooltip title={t('domain.editQuestion')}>
            <IconButton size="small" color="secondary" onClick={e => onEdit(e)} disabled={processing}>
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton size="small" color="error" onClick={e => onDelete(e)} disabled={processing}>
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      }
    >
      <ListItemIcon sx={{ minWidth: 32, color: 'secondary.main' }}>
        <HelpOutlineIcon fontSize="small" />
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography
              variant="body2"
              sx={{
                whiteSpace: 'normal',
                wordBreak: 'break-word',
                paddingRight: '28px' // 留出删除按钮的空间
              }}
            >
              {question.question}
            </Typography>
            {question.answered && (
              <Chip
                size="small"
                label={t('datasets.answered')}
                color="success"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
            )}
          </Box>
        }
      />
    </ListItem>
  );
}
