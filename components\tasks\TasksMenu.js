'use client';

import React, { useState, useEffect } from 'react';
import { Badge, IconButton, Tooltip, Box, CircularProgress, Menu, MenuItem, ListItemIcon, ListItemText, Typography } from '@mui/material';
import TaskAltIcon from '@mui/icons-material/TaskAlt';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import useFileProcessingStatus from '@/hooks/useFileProcessingStatus';
import axios from 'axios';
import TaskIcon from '../TaskIcon';

// 任务菜单组件
export default function TasksMenu({ projectId, theme }) {
  const { t } = useTranslation();
  const router = useRouter();
  const [tasks, setTasks] = useState([]);
  const [polling, setPolling] = useState(false);
  const { setTaskFileProcessing, setTask } = useFileProcessingStatus();
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  // 获取项目的未完成任务列表
  const fetchPendingTasks = async () => {
    if (!projectId) return;

    try {
      const response = await axios.get(`/api/projects/${projectId}/tasks/list?status=0`);
      if (response.data?.code === 0) {
        const tasks = response.data.data || [];
        setTasks(tasks);
        // 检查是否有文件处理任务正在进行
        const hasActiveFileTask = tasks.some(
          task => task.projectId === projectId && task.taskType === 'file-processing'
        );
        setTaskFileProcessing(hasActiveFileTask);
        //存在文件处理任务，将任务信息传递给共享状态
        if (hasActiveFileTask) {
          const activeTask = tasks.find(task => task.projectId === projectId && task.taskType === 'file-processing');
          // 解析任务详情信息
          const detailInfo = JSON.parse(activeTask.detail);
          setTask(detailInfo);
        }
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  };

  // 初始化时获取任务列表
  useEffect(() => {
    if (projectId) {
      fetchPendingTasks();

      // 启动轮询
      const intervalId = setInterval(() => {
        fetchPendingTasks();
      }, 10000); // 每10秒轮询一次

      setPolling(true);

      return () => {
        clearInterval(intervalId);
        setPolling(false);
      };
    }
  }, [projectId]);

  // 打开任务列表页面
  const handleOpenTaskList = () => {
    router.push(`/projects/${projectId}/tasks`);
  };

  // 处理菜单打开
  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // 处理菜单关闭
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // 图标渲染逻辑
  const renderTaskIcon = () => {
    const pendingTasks = tasks.filter(task => task.status === 0);

    if (pendingTasks.length > 0) {
      // 当有任务处理中时，显示 loading 状态同时保留徽标
      return (
        <Badge badgeContent={pendingTasks.length} color="error">
          <CircularProgress size={20} color="inherit" />
        </Badge>
      );
    }

    // 没有处理中的任务时，显示完成图标
    return <TaskAltIcon fontSize="small" />;
  };

  // 悬停提示文本
  const getTooltipText = () => {
    const pendingTasks = tasks.filter(task => task.status === 0);

    if (pendingTasks.length > 0) {
      return t('tasks.pending', { count: pendingTasks.length });
    }

    return t('tasks.completed');
  };

  if (!projectId) return null;

  return (
    <>
      <Tooltip title={getTooltipText()}>
        <IconButton
          onClick={handleMenuOpen}
          size="small"
          sx={{
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.15)',
            color: theme.palette.mode === 'dark' ? 'inherit' : 'white',
            p: 1,
            borderRadius: 1.5,
            '&:hover': {
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.25)'
            },
            ml: 2
          }}
        >
          {renderTaskIcon()}
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 3,
          sx: {
            minWidth: 250,
            maxWidth: 350,
            maxHeight: 400,
            overflow: 'auto',
            mt: 1.5,
            borderRadius: 2,
            p: 1
          }
        }}
      >
        <Box sx={{ p: 1, pb: 2 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            {t('tasks.activeTasks')}
          </Typography>
        </Box>

        {tasks.filter(task => task.status === 0).length === 0 ? (
          <MenuItem disabled>
            <ListItemText primary={t('tasks.noActiveTasks')} />
          </MenuItem>
        ) : (
          tasks
            .filter(task => task.status === 0)
            .map(task => (
              <MenuItem key={task.id} onClick={handleMenuClose}>
                <ListItemIcon>
                  <TaskIcon taskType={task.taskType} tooltip={false} fontSize="small" />
                </ListItemIcon>
                <ListItemText 
                  primary={getTaskName(task.taskType, t)} 
                  secondary={
                    <Typography variant="body2" noWrap>
                      {task.detail || t('tasks.processing')}
                    </Typography>
                  }
                />
              </MenuItem>
            ))
        )}

        <Box sx={{ p: 1, pt: 2 }}>
          <MenuItem onClick={handleOpenTaskList}>
            <ListItemText primary={t('tasks.viewAllTasks')} />
          </MenuItem>
        </Box>
      </Menu>
    </>
  );
}

// 根据任务类型获取任务名称
function getTaskName(taskType, t) {
  switch (taskType) {
    case 'question-generation':
      return t('tasks.types.questionGeneration');
    case 'answer-generation':
      return t('tasks.types.answerGeneration');
    case 'file-processing':
      return t('tasks.types.fileProcessing');
    case 'data-distillation':
      return t('tasks.types.dataDistillation');
    case 'dataset-import':
      return t('tasks.types.datasetImport');
    default:
      return t('tasks.types.unknown');
  }
} 