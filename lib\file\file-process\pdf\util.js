import { getProjectRoot } from '@/lib/db/base';
import { downloadDocument } from '@/lib/oss-simple';
import { getUploadFileInfoById } from '@/lib/db/upload-files';
import path from 'path';

export async function getFilePageCount(projectId, fileList) {
  let totalPages = 0;
  const { getPageNum } = await import('pdf2md-js');
  
  for (const file of fileList) {
    if (file.fileName.endsWith('.pdf')) {
      try {
        // 从OSS下载PDF文件到临时位置
        console.log(`从OSS下载PDF文件: ${file.fileName}, fileId: ${file.fileId}`);
        const fileResult = await downloadDocument(file.fileId);
        
        // 创建临时文件路径
        const tempDir = path.join(process.cwd(), 'temp');
        const tempFilePath = path.join(tempDir, file.fileName);
        
        // 确保临时目录存在
        const fs = await import('fs');
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        
        // 将PDF内容写入临时文件
        fs.writeFileSync(tempFilePath, Buffer.from(fileResult.content, 'binary'));
        
        // 获取页数
        const pageCount = await getPageNum(tempFilePath);
        totalPages += pageCount;
        file.pageCount = pageCount;
        
        // 清理临时文件
        fs.unlinkSync(tempFilePath);
        
        console.log(`PDF文件 ${file.fileName} 页数: ${pageCount}`);
      } catch (error) {
        console.error(`Failed to get page count for ${file.fileName}:`, error);
        // 如果获取页数失败，设置为1页
        totalPages += 1;
        file.pageCount = 1;
      }
    } else {
      totalPages += 1;
      file.pageCount = 1;
    }
  }
  console.log(`Total pages to process: ${totalPages}`);
  return totalPages;
}
