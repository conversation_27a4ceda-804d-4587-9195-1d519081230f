/**
 * 文件Buffer处理工具函数
 */

import { createHash } from 'crypto';

/**
 * 计算Buffer的MD5哈希值
 * @param {Buffer} buffer - 文件内容Buffer
 * @returns {Promise<string>} MD5哈希值
 */
export async function getFileMD5Buffer(buffer) {
  return new Promise((resolve, reject) => {
    try {
      const hash = createHash('md5');
      hash.update(buffer);
      resolve(hash.digest('hex'));
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 计算Buffer的SHA256哈希值
 * @param {Buffer} buffer - 文件内容Buffer
 * @returns {Promise<string>} SHA256哈希值
 */
export async function getFileSHA256Buffer(buffer) {
  return new Promise((resolve, reject) => {
    try {
      const hash = createHash('sha256');
      hash.update(buffer);
      resolve(hash.digest('hex'));
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 验证Buffer的完整性
 * @param {Buffer} buffer - 文件内容Buffer
 * @param {string} expectedHash - 期望的哈希值
 * @param {string} algorithm - 哈希算法 ('md5' | 'sha256')
 * @returns {Promise<boolean>} 验证结果
 */
export async function verifyBufferIntegrity(buffer, expectedHash, algorithm = 'md5') {
  try {
    let actualHash;
    
    switch (algorithm.toLowerCase()) {
      case 'md5':
        actualHash = await getFileMD5Buffer(buffer);
        break;
      case 'sha256':
        actualHash = await getFileSHA256Buffer(buffer);
        break;
      default:
        throw new Error(`Unsupported hash algorithm: ${algorithm}`);
    }
    
    return actualHash.toLowerCase() === expectedHash.toLowerCase();
  } catch (error) {
    console.error('Buffer integrity verification failed:', error);
    return false;
  }
}

/**
 * 获取Buffer的基本信息
 * @param {Buffer} buffer - 文件内容Buffer
 * @returns {Object} Buffer信息
 */
export function getBufferInfo(buffer) {
  if (!Buffer.isBuffer(buffer)) {
    throw new Error('Input is not a Buffer');
  }
  
  return {
    size: buffer.length,
    isEmpty: buffer.length === 0,
    sizeFormatted: formatBytes(buffer.length)
  };
}

/**
 * 格式化字节数为可读格式
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查Buffer是否为特定文件类型
 * @param {Buffer} buffer - 文件内容Buffer
 * @param {string} fileType - 文件类型 ('pdf', 'docx', 'zip', etc.)
 * @returns {boolean} 是否匹配
 */
export function checkBufferFileType(buffer, fileType) {
  if (!Buffer.isBuffer(buffer) || buffer.length < 4) {
    return false;
  }
  
  // 文件魔数签名
  const signatures = {
    pdf: [0x25, 0x50, 0x44, 0x46], // %PDF
    docx: [0x50, 0x4B, 0x03, 0x04], // ZIP signature (DOCX is ZIP-based)
    zip: [0x50, 0x4B, 0x03, 0x04],
    png: [0x89, 0x50, 0x4E, 0x47],
    jpg: [0xFF, 0xD8, 0xFF],
    gif: [0x47, 0x49, 0x46, 0x38]
  };
  
  const signature = signatures[fileType.toLowerCase()];
  if (!signature) {
    return false;
  }
  
  // 检查文件头是否匹配
  for (let i = 0; i < signature.length; i++) {
    if (buffer[i] !== signature[i]) {
      return false;
    }
  }
  
  return true;
}

/**
 * 压缩Buffer（使用gzip）
 * @param {Buffer} buffer - 原始Buffer
 * @returns {Promise<Buffer>} 压缩后的Buffer
 */
export async function compressBuffer(buffer) {
  const zlib = await import('zlib');
  
  return new Promise((resolve, reject) => {
    zlib.gzip(buffer, (error, compressed) => {
      if (error) {
        reject(error);
      } else {
        resolve(compressed);
      }
    });
  });
}

/**
 * 解压Buffer（使用gzip）
 * @param {Buffer} compressedBuffer - 压缩的Buffer
 * @returns {Promise<Buffer>} 解压后的Buffer
 */
export async function decompressBuffer(compressedBuffer) {
  const zlib = await import('zlib');
  
  return new Promise((resolve, reject) => {
    zlib.gunzip(compressedBuffer, (error, decompressed) => {
      if (error) {
        reject(error);
      } else {
        resolve(decompressed);
      }
    });
  });
}
