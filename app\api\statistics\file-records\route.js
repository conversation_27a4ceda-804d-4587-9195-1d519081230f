import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    const projectId = request.nextUrl.searchParams.get('projectId');
    const country = request.nextUrl.searchParams.get('country');

    // 构建查询条件
    let whereClause = {};

    if (projectId) {
      // 如果指定了项目ID，直接查询该项目
      whereClause.projectId = projectId;
    } else if (country) {
      // 如果指定了国家，先查找该国家的所有项目
      const projects = await prisma.projects.findMany({
        where: { country: country },
        select: { id: true }
      });

      if (projects.length === 0) {
        // 该国家没有项目，返回空数据
        return NextResponse.json({
          inData: Array(6).fill(0),
          outData: Array(6).fill(0),
          timeList: generateMonthlyTimeList(),
          totalIn: 0,
          totalOut: 0
        });
      }

      whereClause.projectId = {
        in: projects.map(p => p.id)
      };
    }

    // 获取当前月份的开始和结束时间
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // 添加时间范围条件
    whereClause.createAt = {
      gte: startOfMonth,
      lte: endOfMonth
    };

    // 获取本月的文件记录
    const fileRecords = await prisma.fileRecords.findMany({
      where: whereClause,
      select: {
        operationType: true,
        createAt: true,
        fileSize: true
      },
      orderBy: {
        createAt: 'asc'
      }
    });

    // 生成时间轴
    const timeList = generateMonthlyTimeList();
    const daysInMonth = endOfMonth.getDate();

    // 先汇总所有数据（保持高精度，单位：字节）
    let totalInBytes = 0;
    let totalOutBytes = 0;
    const inDataBytes = Array(6).fill(0);
    const outDataBytes = Array(6).fill(0);

    fileRecords.forEach(record => {
      const recordDate = new Date(record.createAt);
      const dayOfMonth = recordDate.getDate();

      // 计算该记录属于哪个时间段（0-5）
      const timeIndex = Math.min(Math.floor((dayOfMonth - 1) / (daysInMonth / 6)), 5);

      const fileSize = record.fileSize || 0;

      // 根据操作类型分类统计（累加文件大小，保持字节精度）
      if (record.operationType === 'import' || record.operationType === 'upload') {
        inDataBytes[timeIndex] += fileSize;
        totalInBytes += fileSize;
      } else if (record.operationType === 'export') {
        outDataBytes[timeIndex] += fileSize;
        totalOutBytes += fileSize;
      }
    });

    // 汇总完成后，转换为MB并保留3位小数
    const totalInMB = totalInBytes / (1024 * 1024);
    const totalOutMB = totalOutBytes / (1024 * 1024);

    // 将时间段数据转换为MB，保留3位小数
    const inData = inDataBytes.map(bytes => Math.round(bytes / (1024 * 1024) * 1000) / 1000);
    const outData = outDataBytes.map(bytes => Math.round(bytes / (1024 * 1024) * 1000) / 1000);

    // 计算平均处理数据(天) - 基于本月的平均每日处理量（MB，保留3位小数）
    const currentDay = now.getDate();
    const avgDailyProcessing = Math.round((totalInMB + totalOutMB) / currentDay * 1000) / 1000;

    // 计算流量峰值 - 取入库和出库数据中的最大值（MB，保留3位小数）
    const peakFlow = Math.round(Math.max(...inData, ...outData) * 1000) / 1000;

    return NextResponse.json({
      inData, // 已经是MB，保留3位小数
      outData, // 已经是MB，保留3位小数
      timeList,
      totalIn: Math.round(totalInMB * 1000) / 1000, // MB，保留3位小数
      totalOut: Math.round(totalOutMB * 1000) / 1000, // MB，保留3位小数
      recordCount: fileRecords.length,
      avgDailyProcessing, // 平均处理数据(天) - MB，保留3位小数
      peakFlow // 流量峰值 - MB，保留3位小数
    });

  } catch (error) {
    console.error('获取文件记录统计数据失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// 生成本月日期时间轴
function generateMonthlyTimeList() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();

  // 获取本月的天数
  const daysInMonth = new Date(year, month + 1, 0).getDate();

  // 将本月分为6个时间节点
  const timeList = [];
  for (let i = 0; i < 6; i++) {
    // 计算每个节点对应的日期（均匀分布）
    const dayOfMonth = Math.round((daysInMonth / 5) * i) + 1;
    // 确保不超过本月最大天数
    const actualDay = Math.min(dayOfMonth, daysInMonth);

    const monthStr = String(month + 1).padStart(2, '0');
    const dayStr = String(actualDay).padStart(2, '0');
    timeList.push(`${monthStr}/${dayStr}`);
  }
  return timeList;
}
