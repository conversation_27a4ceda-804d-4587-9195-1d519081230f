import { NextResponse } from 'next/server';
import { createDataset, updateDataset } from '@/lib/db/datasets';
import { nanoid } from 'nanoid';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { getTags } from '@/lib/db/tags';
import { getProject } from '@/lib/db/projects';
import LLMClient from '@/lib/llm/core/index';
import { extractJsonFromLLMOutput } from '@/lib/llm/common/util';
import getAddLabelPrompt from '@/lib/llm/prompts/addLabel';
import getAddLabelEnPrompt from '@/lib/llm/prompts/addLabelEn';
import { getActiveModel } from '@/lib/services/models';
import { processTask } from '@/lib/services/tasks';
import { createFileRecord } from '@/lib/db/file-records';
import { generateFileRecordData } from '@/lib/util/file-record-helper';
import { FILE_RECORD } from '@/constant/index';

/**
 * 处理文件导入为数据集
 * @param {Request} request 请求对象
 * @param {Object} params 路由参数
 * @returns {Promise<NextResponse>}
 */
export async function POST(request, { params }) {
  try {
    const { projectId } = params;

    // 检查项目ID是否存在
    if (!projectId) {
      return NextResponse.json(
          {
            code: 400,
            error: '项目ID不能为空'
          },
          { status: 400 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const fileFormat = formData.get('fileFormat') || 'excel'; // 获取文件格式

    if (!file) {
      return NextResponse.json(
          {
            code: 400,
            error: '未提供文件'
          },
          { status: 400 }
      );
    }

    // 检查文件类型
    const fileName = file.name.toLowerCase();
    let isValidType = false;

    switch(fileFormat) {
      case 'excel':
        isValidType = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
        break;
      case 'json':
        isValidType = fileName.endsWith('.json');
        break;
      case 'jsonl':
        isValidType = fileName.endsWith('.jsonl') || fileName.endsWith('.json');
        break;
      case 'csv':
        isValidType = fileName.endsWith('.csv');
        break;
      default:
        isValidType = false;
    }

    if (!isValidType) {
      return NextResponse.json(
          {
            code: 400,
            error: `不支持的文件类型: ${fileName}`
          },
          { status: 400 }
      );
    }

    // 获取导入选项
    const importQuestion = formData.get('importQuestion') === 'true'; // A列：问题
    const importAnswer = formData.get('importAnswer') === 'true'; // B列：答案
    const importDomainTag = formData.get('importDomainTag') === 'true'; // C列：领域标签
    const importCot = formData.get('importCot') === 'true'; // D列：思维链
    const importInstruction = formData.get('importInstruction') === 'true'; // E列：指令
    const autoDetectDomainTag = formData.get('autoDetectDomainTag') === 'true'; // 自动识别领域标签
    const useCustomInstruction = formData.get('useCustomInstruction') === 'true'; // 是否使用自定义指令
    const customInstruction = formData.get('customInstruction') || ''; // 自定义指令内容

    // 新增格式选项（与导出功能一致）
    const formatType = formData.get('formatType') || 'alpaca'; // 格式类型
    const customFields = {
      questionField: formData.get('customFields.questionField') || 'question',
      answerField: formData.get('customFields.answerField') || 'answer',
      cotField: formData.get('customFields.cotField') || 'cot',
      includeLabels: formData.get('includeLabels') === 'true',
      includeChunk: formData.get('includeChunk') === 'true'
    };

    // 获取项目语言
    let language = 'zh-CN'; // 默认使用中文
    try {
      const projectConfig = await getProject(projectId);
      if (projectConfig && projectConfig.language) {
        language = projectConfig.language;
      }
    } catch (error) {
      console.error('获取项目配置失败:', error);
    }

    // 将文件保存到临时目录
    const tempDir = path.join(os.tmpdir(), 'easy-dataset-imports');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const fileBuffer = Buffer.from(await file.arrayBuffer());
    const tempFilePath = path.join(tempDir, `${nanoid()}-${file.name}`);
    fs.writeFileSync(tempFilePath, fileBuffer);

    // 创建文件记录
    let fileRecordId = null;
    try {
      const fileRecordData = generateFileRecordData({
        projectId,
        fileName: file.name,
        originalName: file.name,
        fileBuffer,
        operationType: FILE_RECORD.OPERATION_TYPE.IMPORT,
        filePath: tempFilePath,
        description: `导入${fileFormat.toUpperCase()}数据集文件`,
        metadata: {
          fileFormat,
          importOptions: {
            importQuestion,
            importAnswer,
            importDomainTag,
            importCot,
            importInstruction,
            autoDetectDomainTag,
            useCustomInstruction,
            customInstruction
          }
        }
      });

      const fileRecord = await createFileRecord(fileRecordData);
      fileRecordId = fileRecord.id;
    } catch (error) {
      console.error('创建文件记录失败:', error);
      // 不影响主流程，只记录错误
    }

    // 获取项目的默认模型配置（用于自动识别标签）
    let modelInfo = null;
    if (autoDetectDomainTag) {
      try {
        modelInfo = await getActiveModel(projectId);
        if (!modelInfo) {
          return NextResponse.json(
              {
                code: 400,
                error: '未找到项目默认模型配置，无法进行自动识别标签'
              },
              { status: 400 }
          );
        }
      } catch (error) {
        console.error('获取模型配置失败:', error);
        return NextResponse.json(
            {
              code: 500,
              error: `获取模型配置失败: ${error.message}`
            },
            { status: 500 }
        );
      }
    }

    // 创建导入任务
    const taskData = {
      projectId,
      taskType: 'dataset-import',
      status: 0, // 初始状态: 处理中
      modelInfo: modelInfo ? JSON.stringify(modelInfo) : '{}',
      language: language,
      detail: `导入${fileFormat.toUpperCase()}文件: ${file.name}`,
      totalCount: 0,
      completedCount: 0,
      note: JSON.stringify({
        filePath: tempFilePath,
        fileName: file.name,
        fileFormat,
        fileRecordId, // 关联文件记录ID
        importOptions: {
          importQuestion,
          importAnswer,
          importDomainTag,
          importCot,
          importInstruction,
          autoDetectDomainTag,
          useCustomInstruction,
          customInstruction,
          formatType,
          customFields
        }
      })
    };

    // 创建任务记录
    const { db } = await import('@/lib/db');
    const newTask = await db.task.create({
      data: taskData
    });

    // 异步启动任务处理
    processTask(newTask.id).catch(err => {
      console.error(`Task startup failed: ${newTask.id}`, String(err));
    });

    return NextResponse.json({
      code: 0,
      success: true,
      message: '导入任务已创建，系统将在后台处理导入',
      taskId: newTask.id
    });
  } catch (error) {
    console.error('创建导入任务失败:', error);
    return NextResponse.json(
        {
          code: 500,
          error: `创建导入任务失败: ${error.message}`
        },
        { status: 500 }
    );
  }
}

/**
 * 在对象中查找可能的字段值
 * @param {Object} item 数据项
 * @param {Array<string>} possibleFields 可能的字段名数组
 * @returns {string|null} 找到的字段值或null
 */
function findFieldValue(item, possibleFields) {
  for (const field of possibleFields) {
    if (item[field] !== undefined && item[field] !== null) {
      return item[field];
    }
  }

  // 检查消息格式（如ShareGPT格式）
  if (item.messages && Array.isArray(item.messages)) {
    for (const message of item.messages) {
      if (message.role === 'user' && possibleFields.includes('question')) {
        return message.content;
      }
      if (message.role === 'assistant' && possibleFields.includes('answer')) {
        return message.content;
      }
    }
  }

  return null;
}

/**
 * 更新数据集的领域标签
 * @param {string} datasetId 数据集ID
 * @param {string} label 领域标签
 */
async function updateDatasetLabel(datasetId, label) {
  try {
    // 使用数据库更新函数直接更新数据集
    await updateDataset({
      id: datasetId,
      questionLabel: label
    });

    return true;
  } catch (error) {
    console.error(`更新数据集 ${datasetId} 的标签失败:`, error);
    throw error;
  }
}
