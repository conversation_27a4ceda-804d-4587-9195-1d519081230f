import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    const country = request.nextUrl.searchParams.get('country'); // 可选的国家参数

    // 国家名称到国家代码的映射（与全局统计API保持一致）
    const countryNameToCode = {
      'Brunei': 'BN',
      'Cambodia': 'KH',
      'Indonesia': 'ID',
      'Laos': 'LA',
      'Malaysia': 'MY',
      'Myanmar': 'MM',
      'Philippines': 'PH',
      'Singapore': 'SG',
      'Thailand': 'TH',
      'Vietnam': 'VN'
    };

    let whereCondition = {};

    // 如果指定了国家，则只查询该国家的项目
    if (country && countryNameToCode[country]) {
      whereCondition = {
        country: countryNameToCode[country]
      };
    }

    // 获取符合条件的项目ID列表
    const projects = await prisma.projects.findMany({
      where: whereCondition,
      select: { id: true }
    });

    const projectIds = projects.map(p => p.id);

    // 如果没有找到项目，返回空数据
    if (projectIds.length === 0) {
      return NextResponse.json({
        questionCount: 0,
        datasetCount: 0,
        importFileCount: 0,
        exportFileCount: 0,
        documentToQuestionCount: 0,
        questionToDatasetCount: 0
      });
    }

    // 并行查询各种统计数据
    const [
      questionCount,
      datasetCount,
      importTaskCount,
      exportTaskCount,
      documentToQuestionCount,
      questionToDatasetCount
    ] = await Promise.all([
      // 问题总数
      prisma.questions.count({
        where: {
          projectId: { in: projectIds }
        }
      }),

      // 数据集总数
      prisma.datasets.count({
        where: {
          projectId: { in: projectIds }
        }
      }),

      // 导入文件总数（包括import和upload操作）
      prisma.fileRecords.count({
        where: {
          projectId: { in: projectIds },
          operationType: { in: ['import', 'upload'] },
          status: 1 // 1表示成功
        }
      }),

      // 导出文件总数
      prisma.fileRecords.count({
        where: {
          projectId: { in: projectIds },
          operationType: 'export',
          status: 1 // 1表示成功
        }
      }),

      // 文献生成问题：统计来自真实文献（非virtual chunks）的问题数量
      prisma.questions.count({
        where: {
          projectId: { in: projectIds },
          chunk: {
            fileId: {
              not: 'virtual' // 排除导入数据集生成的虚拟chunks
            }
          }
        }
      }),

      // 问题生成数据集：统计model字段不为导入类型的数据集数量
      prisma.datasets.count({
        where: {
          projectId: { in: projectIds },
          model: {
            notIn: ['Excel导入', 'CSV导入', 'JSON导入', 'JSONL导入'] // 排除导入的数据集
          }
        }
      })
    ]);

    // 计算真实统计数据

    return NextResponse.json({
      questionCount,
      datasetCount,
      importFileCount: importTaskCount,
      exportFileCount: exportTaskCount,
      documentToQuestionCount,
      questionToDatasetCount
    });

  } catch (error) {
    console.error('获取仪表板统计数据失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
