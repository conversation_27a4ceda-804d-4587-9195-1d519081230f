'use client';

import React from 'react';
import { Tooltip } from '@mui/material';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import PsychologyIcon from '@mui/icons-material/Psychology';
import DescriptionIcon from '@mui/icons-material/Description';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { useTranslation } from 'react-i18next';

/**
 * 任务图标组件
 * 根据任务类型显示不同的图标
 */
const TaskIcon = ({ taskType, tooltip = true, ...props }) => {
  const { t } = useTranslation();

  // 根据任务类型选择图标
  const getIcon = () => {
    switch (taskType) {
      case 'question-generation':
        return <QuestionAnswerIcon {...props} />;
      case 'answer-generation':
        return <PsychologyIcon {...props} />;
      case 'file-processing':
        return <DescriptionIcon {...props} />;
      case 'data-distillation':
        return <FilterAltIcon {...props} />;
      case 'dataset-import':
        return <UploadFileIcon {...props} />;
      case 'batch-auto-tag':
        return <AutoFixHighIcon {...props} />;
      default:
        return <DescriptionIcon {...props} />;
    }
  };

  // 根据任务类型获取提示文本
  const getTooltipText = () => {
    switch (taskType) {
      case 'question-generation':
        return t('tasks.types.question-generation');
      case 'answer-generation':
        return t('tasks.types.answer-generation');
      case 'file-processing':
        return t('tasks.types.file-processing');
      case 'data-distillation':
        return t('tasks.types.data-distillation');
      case 'dataset-import':
        return t('tasks.types.dataset-import');
      case 'batch-auto-tag':
        return t('tasks.types.batch-auto-tag');
      default:
        return t('tasks.types.unknown');
    }
  };

  const icon = getIcon();

  // 如果需要提示，则包装在Tooltip中
  if (tooltip) {
    return (
        <Tooltip title={getTooltipText()}>
          <span>{icon}</span>
        </Tooltip>
    );
  }

  return icon;
};

export default TaskIcon;
