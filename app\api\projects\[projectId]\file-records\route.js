import { NextResponse } from 'next/server';
import { createFileRecord } from '@/lib/db/file-records';

/**
 * 创建文件记录（仅用于内部存储，不对外提供查询功能）
 */
export async function POST(request, { params }) {
  try {
    const { projectId } = params;

    // 验证项目ID
    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    const body = await request.json();
    const {
      fileName,
      originalName,
      operationType,
      fileFormat,
      filePath,
      fileSize,
      md5Hash,
      recordCount,
      description,
      metadata,
      taskId
    } = body;

    // 验证必要字段
    if (!fileName || !operationType) {
      return NextResponse.json(
        { error: '文件名和操作类型不能为空' },
        { status: 400 }
      );
    }

    // 创建文件记录数据
    const fileRecordData = {
      projectId,
      fileName,
      originalName: originalName || fileName,
      fileExt: fileName.substring(fileName.lastIndexOf('.')),
      fileFormat: fileFormat || 'unknown',
      operationType,
      filePath,
      fileSize: fileSize || 0,
      md5Hash,
      mimeType: null,
      status: 1, // 默认成功状态
      recordCount,
      description,
      metadata: metadata ? JSON.stringify(metadata) : null,
      taskId
    };

    // 创建文件记录
    const fileRecord = await createFileRecord(fileRecordData);

    return NextResponse.json({ success: true, id: fileRecord.id }, { status: 201 });
  } catch (error) {
    console.error('创建文件记录失败:', error);
    return NextResponse.json(
      { error: '创建文件记录失败' },
      { status: 500 }
    );
  }
}
