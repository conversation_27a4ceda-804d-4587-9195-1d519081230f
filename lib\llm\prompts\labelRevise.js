/**
 * 领域树增量修订提示词
 * 用于在已有领域树的基础上，针对新增/删除的文献内容，对领域树进行增量调整
 */
function getLabelRevisePrompt({ text, existingTags, deletedContent, newContent, globalPrompt, domainTreePrompt }) {
  const prompt = `


## 现有领域树
${JSON.stringify(existingTags, null, 2)}

## 原始目录
${text}

## 删除的内容
${deletedContent || '无'}

## 新增的内容
${newContent || '无'}

## 要求
请分析上述信息，修订现有的领域树结构，遵循以下原则：
1. 保持领域树的总体结构稳定，避免大规模重构
2. 对于删除的内容相关的领域标签：
   - 如果某个标签仅与删除的内容相关，且在现有文献中找不到相应内容支持，则移除该标签
   - 如果某个标签同时与其他保留的内容相关，则保留该标签
3. 对于新增的内容：
   - 如果新内容可以归类到现有的标签中，优先使用现有标签
   - 如果新内容引入了现有标签体系中没有的新领域或概念，再创建新的标签
4. 每个标签必须对应目录结构中的实际内容，不要创建没有对应内容支持的空标签
5. 确保修订后的领域树仍然符合良好的层次结构，标签间具有合理的父子关系

## 限制
1. 一级领域标签数量5-10个
2. 二级领域标签数量1-10个
3. 标签最多两层分类层级
4. 分类必须与原始目录内容相关
5. 输出必须符合指定 JSON 格式，不要输出 JSON 外其他任何不相关内容
6. 标签的名字最多不要超过 6 个字
7. 标签格式规范：
   - 一级标签格式：使用中文数字加顿号，如"二、社交互动"
   - 二级标签格式：使用阿拉伯数字（小数点前阿拉伯数字代表一级标签，小数点后代表顺序）加小数点，如"2.2 日常社交"

## 输出格式
最终输出修订后的完整领域树结构，使用下面的JSON格式：

\`\`\`json
[
  {
    "label": "一、社交互动",
    "child": [
      {"label": "1.1 日常社交"},
      {"label": "1.2 情感交流"}
    ]
  },
  {
    "label": "二、学习教育",
    "child": [
      {"label": "2.1 学科知识"}
    ]
  }
]
\`\`\`

确保你的回答中只包含JSON格式的领域树，不要有其他解释性文字。`;

  return prompt;
}

module.exports = getLabelRevisePrompt;
