apiVersion: v1
kind: Service
metadata:
  name: easy-dataset-dev
  labels:
    cloud.sealos.io/app-deploy-manager: easy-dataset-dev
spec:
  ports:
    - port: 1717
      targetPort: 1717
      name: qsqxlvoaqcqp
      protocol: TCP
  selector:
    app: easy-dataset-dev
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: easy-dataset-dev
  annotations:
    originImageName: easy-dataset/dev/app:${tag}
    deploy.cloud.sealos.io/minReplicas: '1'
    deploy.cloud.sealos.io/maxReplicas: '1'
    deploy.cloud.sealos.io/resize: 0Gi
  labels:
    app: easy-dataset-dev
    cloud.sealos.io/app-deploy-manager: easy-dataset-dev
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: easy-dataset-dev
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: easy-dataset-dev
        restartTime: '**************'
    spec:
      automountServiceAccountToken: false
      imagePullSecrets:
        - name: easy-dataset-dev
      containers:
        - name: easy-dataset-dev
          image: gui-ygzkgxit.sealosgzg.site/easy-dataset/dev/app:${tag}
          env: []
          resources:
            requests:
              cpu: 100m
              memory: 204Mi
            limits:
              cpu: 1000m
              memory: 2048Mi
          ports:
            - containerPort: 1717
              name: qsqxlvoaqcqp
          imagePullPolicy: Always
          volumeMounts: []
      volumes: []
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: network-mrewtpohshkh
  labels:
    cloud.sealos.io/app-deploy-manager: easy-dataset-dev
    cloud.sealos.io/app-deploy-manager-domain: gjcmoydsebwk
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: gjcmoydsebwk.sealosgzg.site
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: easy-dataset-dev
                port:
                  number: 1717
  tls:
    - hosts:
        - gjcmoydsebwk.sealosgzg.site
      secretName: wildcard-cert
---
apiVersion: v1
kind: Secret
metadata:
  name: easy-dataset-dev
data:
  .dockerconfigjson: >-
    ************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/dockerconfigjson
