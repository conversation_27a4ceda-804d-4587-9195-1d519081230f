'use client';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import axios from 'axios';
import * as XLSX from 'xlsx';
import { FILE_RECORD } from '@/constant/index';

const useDatasetExport = projectId => {
  const { t } = useTranslation();

  // 导出数据集
  const exportDatasets = async exportOptions => {
    try {
      let apiUrl = `/api/projects/${projectId}/datasets/export`;
      if (exportOptions.confirmedOnly) {
        apiUrl += `?status=confirmed`;
      }
      const response = await axios.get(apiUrl);
      let dataToExport = response.data;

      // 根据选择的格式转换数据
      let formattedData;
      // 不同文件格式
      let mimeType = 'application/json';

      // 如果是Excel格式，使用与导入格式一致的结构
      if (exportOptions.fileFormat === 'excel') {
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 根据用户选择动态创建表头
        const header = [];
        const colWidths = [];

        // 问题列 - 必选
        header.push("问题");
        colWidths.push({ wch: 50 });

        // 答案列 - 必选
        header.push("答案");
        colWidths.push({ wch: 50 });

        // 领域标签列 - 可选
        if (exportOptions.excelColumns?.includeDomainTag) {
          header.push("领域标签");
          colWidths.push({ wch: 20 });
        }

        // 思维链列 - 可选
        if (exportOptions.excelColumns?.includeCot) {
          header.push("思维链");
          colWidths.push({ wch: 50 });
        }

        // 指令列 - 可选
        if (exportOptions.excelColumns?.includeInstruction) {
          header.push("指令");
          colWidths.push({ wch: 50 });
        }

        // 创建数据行
        const excelData = [header];

        // 添加数据行
        dataToExport.forEach((item, index) => {
          const row = [];

          // 问题列 - 必选
          row.push(item.question);

          // 答案列 - 必选
          row.push(item.answer || '');

          // 领域标签列 - 可选
          if (exportOptions.excelColumns?.includeDomainTag) {
            row.push(item.questionLabel || '其他');
          }

          // 思维链列 - 可选
          if (exportOptions.excelColumns?.includeCot) {
            row.push(exportOptions.includeCOT ? (item.cot || '') : '');
          }

          // 指令列 - 可选
          if (exportOptions.excelColumns?.includeInstruction) {
            row.push(item.instruction || '');
          }

          excelData.push(row);
        });

        // 将数据转换为工作表
        const ws = XLSX.utils.aoa_to_sheet(excelData);

        // 设置列宽
        ws['!cols'] = colWidths;

        // 将工作表添加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "数据集");

        // 生成Excel文件并触发下载
        const fileName = `datasets-${projectId}-${new Date().toISOString().slice(0, 10)}.xlsx`;

        // 将工作簿转换为Excel文件并下载
        XLSX.writeFile(wb, fileName);

        // 计算Excel文件大小（估算）
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'buffer' });
        const fileSize = excelBuffer.length;

        // 记录导出文件
        await recordExportFile(fileName, 'excel', dataToExport.length, fileSize);

        toast.success(t('datasets.exportSuccess'));
        return true;
      } else if (exportOptions.formatType === 'alpaca') {
        // Alpaca 格式：使用 instruction 字段
        formattedData = dataToExport.map(({ question, answer, cot, instruction }) => ({
          instruction: instruction || question,
          input: question,
          output: cot && exportOptions.includeCOT ? `<think>${cot}</think>\n${answer}` : answer,
          system: exportOptions.systemPrompt || ''
        }));
      } else if (exportOptions.formatType === 'sharegpt') {
        formattedData = dataToExport.map(({ question, answer, cot, instruction }) => {
          const messages = [];

          // 添加系统提示词（如果有）
          const systemContent = instruction || exportOptions.systemPrompt;
          if (systemContent) {
            messages.push({
              role: 'system',
              content: systemContent
            });
          }

          // 添加用户问题
          messages.push({
            role: 'user',
            content: question
          });

          // 添加助手回答
          messages.push({
            role: 'assistant',
            content: cot && exportOptions.includeCOT ? `<think>${cot}</think>\n${answer}` : answer
          });

          return { messages };
        });
      } else if (exportOptions.formatType === 'custom') {
        // 处理自定义格式
        const { questionField, answerField, cotField, includeLabels, includeChunk } = exportOptions.customFields;
        formattedData = dataToExport.map(({ question, answer, cot, instruction, questionLabel: labels, chunkId }) => {
          const item = {
            [questionField]: question,
            [answerField]: answer
          };

          // 添加instruction字段
          if (instruction) {
            item.instruction = instruction;
          }

          // 如果有思维链且用户选择包含思维链，则添加思维链字段
          if (cot && exportOptions.includeCOT && cotField) {
            item[cotField] = cot;
          }

          // 如果需要包含标签
          if (includeLabels && labels && labels.length > 0) {
            item.label = labels.split(' ')[1];
          }

          // 如果需要包含文本块
          if (includeChunk && chunkId) {
            item.chunk = chunkId;
          }

          return item;
        });
      }

      // 处理不同的文件格式
      let content;
      let fileExtension;

      if (exportOptions.fileFormat === 'jsonl') {
        // JSONL 格式：每行一个 JSON 对象
        content = formattedData.map(item => JSON.stringify(item)).join('\n');
        fileExtension = 'jsonl';
      } else if (exportOptions.fileFormat === 'csv') {
        // CSV 格式
        const headers = Object.keys(formattedData[0] || {});
        const csvRows = [
          // 添加表头
          headers.join(','),
          // 添加数据行
          ...formattedData.map(item =>
              headers
                  .map(header => {
                    // 处理包含逗号、换行符或双引号的字段
                    let field = item[header]?.toString() || '';
                    if (exportOptions.formatType === 'sharegpt') field = JSON.stringify(item[header]);
                    if (field.includes(',') || field.includes('\n') || field.includes('"')) {
                      field = `"${field.replace(/"/g, '""')}"`;
                    }
                    return field;
                  })
                  .join(',')
          )
        ];
        content = csvRows.join('\n');
        fileExtension = 'csv';
      } else {
        // 默认 JSON 格式
        content = JSON.stringify(formattedData, null, 2);
        fileExtension = 'json';
      }

      // 如果不是Excel格式，则创建Blob并下载
      if (exportOptions.fileFormat !== 'excel') {
        // 创建 Blob 对象
        const blob = new Blob([content], { type: mimeType || 'application/json' });

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        const formatSuffix = exportOptions.formatType === 'alpaca' ? 'alpaca' : exportOptions.formatType;
        a.download = `datasets-${projectId}-${formatSuffix}-${new Date().toISOString().slice(0, 10)}.${fileExtension}`;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 记录导出文件
        await recordExportFile(a.download, fileExtension, dataToExport.length, blob.size);

        toast.success(t('datasets.exportSuccess'));
      }

      return true;
    } catch (error) {
      toast.error(error.message);
      return false;
    }
  };

  // 记录导出文件的辅助函数
  const recordExportFile = async (fileName, fileFormat, recordCount, fileSize = 0) => {
    try {
      await axios.post(`/api/projects/${projectId}/file-records`, {
        fileName,
        originalName: fileName,
        operationType: FILE_RECORD.OPERATION_TYPE.EXPORT,
        fileFormat,
        fileSize,
        recordCount,
        description: `导出${recordCount}条数据集记录`,
        metadata: {
          exportOptions: {
            formatType: fileFormat,
            recordCount,
            fileSize
          }
        }
      });
    } catch (error) {
      console.error('记录导出文件失败:', error);
      // 不影响导出流程
    }
  };

  return { exportDatasets };
};

export default useDatasetExport;
