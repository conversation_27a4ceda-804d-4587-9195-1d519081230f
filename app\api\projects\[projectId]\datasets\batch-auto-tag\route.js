import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getTags } from '@/lib/db/tags';
import { getActiveModel } from '@/lib/services/models';
import { processTask } from '@/lib/services/tasks';

/**
 * 批量自动识别领域标签 - 创建任务方式处理
 */
export async function POST(request, { params }) {
  try {
    const { projectId } = params;
    const { datasetIds } = await request.json();

    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    if (!datasetIds || !Array.isArray(datasetIds) || datasetIds.length === 0) {
      return NextResponse.json({ error: '数据集ID列表不能为空' }, { status: 400 });
    }

    // 验证项目是否有领域标签
    const projectTags = await getTags(projectId);
    if (!projectTags || projectTags.length === 0) {
      return NextResponse.json({ error: '项目没有领域标签，无法自动识别' }, { status: 400 });
    }

    // 验证项目是否配置了默认模型
    const modelConfig = await getActiveModel(projectId);
    if (!modelConfig) {
      return NextResponse.json({ error: '项目未配置默认模型，无法进行自动识别' }, { status: 400 });
    }

    // 验证数据集是否存在
    const datasets = await db.datasets.findMany({
      where: {
        id: { in: datasetIds },
        projectId: projectId
      },
      select: {
        id: true
      }
    });

    if (datasets.length === 0) {
      return NextResponse.json({ error: '未找到要处理的数据集' }, { status: 404 });
    }

    // 创建批量自动识别领域标签任务
    const newTask = await db.task.create({
      data: {
        taskType: 'batch-auto-tag',
        projectId: projectId,
        modelInfo: JSON.stringify(modelConfig),
        language: 'zh',
        detail: '准备开始批量自动识别领域标签...',
        totalCount: datasets.length,
        completedCount: 0,
        note: JSON.stringify({
          projectId: projectId,
          datasetIds: datasetIds
        }),
        status: 0, // 处理中
        createAt: new Date(),
        updateAt: new Date()
      }
    });

    // 异步启动任务处理
    processTask(newTask.id).catch(error => {
      console.error(`启动批量自动识别领域标签任务失败: ${newTask.id}`, error);
    });

    return NextResponse.json({
      success: true,
      message: '批量自动识别领域标签任务已创建',
      taskId: newTask.id,
      totalCount: datasets.length
    });

  } catch (error) {
    console.error('创建批量自动识别领域标签任务失败:', error);
    return NextResponse.json(
      { error: `创建任务失败: ${error.message}` },
      { status: 500 }
    );
  }
}
