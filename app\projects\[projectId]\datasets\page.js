'use client';

import { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Typography,
  Button,
  IconButton,
  Paper,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  useTheme,
  alpha,
  InputBase,
  LinearProgress,
  Select,
  MenuItem,
  Stack,
  Popover
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import FilterListIcon from '@mui/icons-material/FilterList';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { useRouter } from 'next/navigation';
import ExportDatasetDialog from '@/components/ExportDatasetDialog';
import ImportDatasetDialog from '@/components/datasets/ImportDatasetDialog';
import DomainTagFilter from '@/components/datasets/DomainTagFilter';
import { useTranslation } from 'react-i18next';
import DatasetList from './components/DatasetList';
import useDatasetExport from './hooks/useDatasetExport';
import { processInParallel } from '@/lib/util/async';
import axios from 'axios';
import { useDebounce } from '@/hooks/useDebounce';
import { toast } from 'sonner';

// 删除确认对话框
const DeleteConfirmDialog = ({ open, datasets, onClose, onConfirm, batch, progress, deleting }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dataset = datasets?.[0];
  return (
      <Dialog
          open={open}
          onClose={onClose}
          PaperProps={{
            elevation: 3,
            sx: { borderRadius: 2 }
          }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" fontWeight="bold">
            {t('common.confirmDelete')}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ pb: 2, pt: 1 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {batch
                ? t('datasets.batchconfirmDeleteMessage', {
                  count: datasets.length
                })
                : t('common.confirmDeleteDataSet')}
          </Typography>
          {batch ? (
              ''
          ) : (
              <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    backgroundColor: alpha(theme.palette.warning.light, 0.1),
                    borderColor: theme.palette.warning.light
                  }}
              >
                <Typography variant="subtitle2" color="text.secondary" fontWeight="bold">
                  {t('datasets.question')}：
                </Typography>
                <Typography variant="body2">{dataset?.question}</Typography>
              </Paper>
          )}
          {deleting && progress ? (
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body1" sx={{ mr: 1 }}>
                    {progress.percentage}%
                  </Typography>
                  <Box sx={{ width: '100%' }}>
                    <LinearProgress
                        variant="determinate"
                        value={progress.percentage}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          '& .MuiLinearProgress-bar': {
                            transitionDuration: '0.1s'
                          }
                        }}
                        color="primary"
                    />
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                  <Typography variant="body2">
                    {t('datasets.batchDeleteProgress', {
                      completed: progress.completed,
                      total: progress.total
                    })}
                  </Typography>
                  <Typography variant="body2" color="success.main" sx={{ fontWeight: 'medium' }}>
                    {t('datasets.batchDeleteCount', { count: progress.datasetCount })}
                  </Typography>
                </Box>
              </Box>
          ) : (
              ''
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={onClose} variant="outlined" sx={{ borderRadius: 2 }}>
            {t('common.cancel')}
          </Button>
          <Button onClick={onConfirm} color="error" variant="contained" sx={{ borderRadius: 2 }}>
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>
  );
};

// 主页面组件
export default function DatasetsPage({ params }) {
  const { projectId } = params;
  const router = useRouter();
  const theme = useTheme();
  const [datasets, setDatasets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    datasets: null,
    batch: false,
    deleting: false
  });
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery);
  const [searchField, setSearchField] = useState('question'); // 新增：筛选字段，默认为问题
  const [exportDialog, setExportDialog] = useState({ open: false });
  const [importDialog, setImportDialog] = useState({ open: false }); // 新增：导入对话框状态
  const [selectedIds, setSelectedIds] = useState([]);
  const [filterConfirmed, setFilterConfirmed] = useState('all');
  const [filterHasCot, setFilterHasCot] = useState('all');
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState(null); // 添加领域标签状态
  const [tagPopoverAnchor, setTagPopoverAnchor] = useState(null); // 添加标签选择弹出框锚点
  const [batchAutoTagLoading, setBatchAutoTagLoading] = useState(false); // 批量自动识别加载状态
  const { t } = useTranslation();
  // 删除进度状态
  const [deleteProgress, setDeteleProgress] = useState({
    total: 0, // 总删除问题数量
    completed: 0, // 已删除完成的数量
    percentage: 0 // 进度百分比
  });

  // 处理领域标签选择
  const handleTagSelect = (tag) => {
    setSelectedTag(tag);
    setPage(1); // 重置页码
    if (tag) {
      setSearchQuery(tag.label); // 将选中的标签显示在搜索框中
    } else {
      setSearchQuery(''); // 清空搜索框
    }
    setTagPopoverAnchor(null); // 关闭弹出框
  };

  // 处理搜索框点击
  const handleSearchFieldClick = (event) => {
    // 只有当选择的是标签字段时，才显示标签选择弹出框
    if (searchField === 'label') {
      setTagPopoverAnchor(event.currentTarget);
    }
  };

  // 处理搜索框输入变化
  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
    // 如果是标签字段，并且用户手动输入了内容，清除已选标签
    if (searchField === 'label' && selectedTag && e.target.value !== selectedTag.label) {
      setSelectedTag(null);
    }
  };

  // 处理搜索字段变化
  const handleSearchFieldChange = (e) => {
    const newField = e.target.value;
    setSearchField(newField);

    // 如果从标签字段切换到其他字段，清除已选标签
    if (newField !== 'label' && selectedTag) {
      setSelectedTag(null);
    }

    // 如果切换到标签字段，清空搜索框
    if (newField === 'label') {
      setSearchQuery('');
    }
  };

  // 关闭标签弹出框
  const handleCloseTagPopover = () => {
    setTagPopoverAnchor(null);
  };

  // 批量自动识别领域标签
   const handleBatchAutoTag = async () => {
    try {
      setBatchAutoTagLoading(true);
      const response = await axios.post(`/api/projects/${projectId}/datasets/batch-auto-tag`, {
        datasetIds: selectedIds
      });
      
      if (response.data.success) {
        toast.success(response.data.message || '批量自动识别任务已创建，系统将在后台处理');
        // 清空选择
        setSelectedIds([]);
      } else {
        toast.error(response.data.error || '创建批量自动识别任务失败');
      }
    } catch (error) {
      console.error('批量自动识别失败:', error);
      toast.error(error.response?.data?.error || '批量自动识别失败');
    } finally {
      setBatchAutoTagLoading(false);
    }
  };

  // 3. 添加打开导出对话框的处理函数
  const handleOpenExportDialog = () => {
    setExportDialog({ open: true });
  };

  // 4. 添加关闭导出对话框的处理函数
  const handleCloseExportDialog = () => {
    setExportDialog({ open: false });
  };

  // 添加打开导入对话框的处理函数
  const handleOpenImportDialog = () => {
    setImportDialog({ open: true });
  };

  // 添加关闭导入对话框的处理函数
  const handleCloseImportDialog = () => {
    setImportDialog({ open: false });
  };

  // 导入成功后的处理函数
  const handleImportSuccess = () => {
    getDatasetsList(); // 刷新数据集列表
  };

  // 获取数据集列表
  const getDatasetsList = async () => {
    try {
      setLoading(true);
      // 构建查询参数
      const params = {
        page,
        size: rowsPerPage,
        status: filterConfirmed,
        input: searchQuery,
        field: searchField,
        hasCot: filterHasCot
      };

      // 添加标签过滤
      if (selectedTag && searchField === 'label') {
        // 如果是父标签且包含子标签信息，将整个标签对象序列化传递
        if (selectedTag.isParentTag && selectedTag.childTags) {
          params.tag = JSON.stringify(selectedTag);
        } else {
          params.tag = selectedTag.label;
        }
        // 当使用标签过滤时，不使用搜索框的值
        params.input = '';
      }

      // 构建查询字符串
      const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined && value !== null && value !== '')
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&');

      const response = await axios.get(`/api/projects/${projectId}/datasets?${queryString}`);
      setDatasets(response.data);
    } catch (error) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getDatasetsList();
  }, [projectId, page, rowsPerPage, filterConfirmed, debouncedSearchQuery, searchField, filterHasCot, selectedTag]);

  // 处理页码变化
  const handlePageChange = (event, newPage) => {
    // MUI TablePagination 的页码从 0 开始，而我们的 API 从 1 开始
    setPage(newPage + 1);
  };

  // 处理每页行数变化
  const handleRowsPerPageChange = event => {
    setPage(1);
    setRowsPerPage(parseInt(event.target.value, 10));
  };

  // 打开删除确认框
  const handleOpenDeleteDialog = dataset => {
    setDeleteDialog({
      open: true,
      datasets: [dataset]
    });
  };

  // 关闭删除确认框
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      dataset: null
    });
  };

  const handleBatchDeleteDataset = async () => {
    const datasetsArray = selectedIds.map(id => ({ id }));
    setDeleteDialog({
      open: true,
      datasets: datasetsArray,
      batch: true,
      count: selectedIds.length
    });
  };

  const resetProgress = () => {
    setDeteleProgress({
      total: deleteDialog.count,
      completed: 0,
      percentage: 0
    });
  };

  const handleDeleteConfirm = async () => {
    if (deleteDialog.batch) {
      setDeleteDialog({
        ...deleteDialog,
        deleting: true
      });
      await handleBatchDelete();
      resetProgress();
    } else {
      const [dataset] = deleteDialog.datasets;
      if (!dataset) return;
      await handleDelete(dataset);
    }
    setSelectedIds([]);
    // 刷新数据
    getDatasetsList();
    // 关闭确认框
    handleCloseDeleteDialog();
  };

  // 批量删除数据集
  const handleBatchDelete = async () => {
    try {
      await processInParallel(
          selectedIds,
          async datasetId => {
            await fetch(`/api/projects/${projectId}/datasets?id=${datasetId}`, {
              method: 'DELETE'
            });
          },
          3,
          (cur, total) => {
            setDeteleProgress({
              total,
              completed: cur,
              percentage: Math.floor((cur / total) * 100)
            });
          }
      );

      toast.success(t('common.deleteSuccess'));
    } catch (error) {
      console.error('批量删除失败:', error);
      toast.error(error.message || t('common.deleteFailed'));
    }
  };

  // 删除数据集
  const handleDelete = async dataset => {
    try {
      const response = await fetch(`/api/projects/${projectId}/datasets?id=${dataset.id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error(t('datasets.deleteFailed'));

      toast.success(t('datasets.deleteSuccess'));
    } catch (error) {
      toast.error(error.message || t('datasets.deleteFailed'));
    }
  };

  // 使用自定义 Hook 处理数据集导出逻辑
  const { exportDatasets } = useDatasetExport(projectId);

  // 处理导出数据集
  const handleExportDatasets = async exportOptions => {
    const success = await exportDatasets(exportOptions);
    if (success) {
      // 关闭导出对话框
      handleCloseExportDialog();
    }
  };

  // 查看详情
  const handleViewDetails = id => {
    router.push(`/projects/${projectId}/datasets/${id}`);
  };

  // 处理全选/取消全选
  const handleSelectAll = async event => {
    if (event.target.checked) {
      // 获取所有符合当前筛选条件的数据，不受分页限制
      try {
        // 构建查询参数
        const params = {
          status: filterConfirmed,
          input: searchQuery,
          field: searchField,
          hasCot: filterHasCot,
          selectedAll: 1
        };

        // 添加标签过滤
        if (selectedTag && searchField === 'label') {
          // 如果是父标签且包含子标签信息，将整个标签对象序列化传递
          if (selectedTag.isParentTag && selectedTag.childTags) {
            params.tag = JSON.stringify(selectedTag);
          } else {
            params.tag = selectedTag.label;
          }
          // 当使用标签过滤时，不使用搜索框的值
          params.input = '';
        }

        // 构建查询字符串
        const queryString = Object.entries(params)
            .filter(([_, value]) => value !== undefined && value !== null && value !== '')
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

        const response = await axios.get(`/api/projects/${projectId}/datasets?${queryString}`);
        setSelectedIds(response.data.map(dataset => dataset.id));
      } catch (error) {
        console.error('获取所有数据失败:', error);
        toast.error(t('common.operationFailed'));
      }
    } else {
      setSelectedIds([]);
    }
  };

  // 处理单个选择
  const handleSelectItem = id => {
    setSelectedIds(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  if (loading) {
    return (
        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '70vh'
              }}
          >
            <CircularProgress size={60} thickness={4} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              {t('datasets.loading')}
            </Typography>
          </Box>
        </Container>
    );
  }

  return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 3 }}>
          <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2
              }}
          >
            <Box>
              <Typography variant="h5" component="h1" fontWeight="bold">
                {t('datasets.management')}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {t('datasets.stats', {
                  total: datasets.total || 0,
                  confirmed: datasets.confirmed || 0,
                  percentage: datasets.total ? Math.round((datasets.confirmed / datasets.total) * 100) : 0
                })}
              </Typography>
            </Box>

            {/* 搜索框和过滤按钮 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, justifyContent: 'center' }}>
              <Paper
                  sx={{
                    p: '2px 4px',
                    display: 'flex',
                    alignItems: 'center',
                    width: 400,
                    borderRadius: 2
                  }}
              >
                <IconButton sx={{ p: '10px' }} aria-label="search">
                  <SearchIcon />
                </IconButton>
                <InputBase
                    sx={{ ml: 1, flex: 1 }}
                    placeholder={searchField === 'label' ? t('datasets.domainTag') : t('datasets.searchPlaceholder')}
                    value={searchQuery}
                    onChange={handleSearchInputChange}
                    onClick={handleSearchFieldClick}
                    readOnly={searchField === 'label'} // 当选择标签字段时，搜索框为只读
                    endAdornment={
                      <Select
                          value={searchField}
                          onChange={handleSearchFieldChange}
                          variant="standard"
                          sx={{
                            minWidth: 90,
                            '& .MuiInput-underline:before': { borderBottom: 'none' },
                            '& .MuiInput-underline:after': { borderBottom: 'none' },
                            '& .MuiInput-underline:hover:not(.Mui-disabled):before': { borderBottom: 'none' }
                          }}
                          disableUnderline
                      >
                        <MenuItem value="question">{t('datasets.fieldQuestion')}</MenuItem>
                        <MenuItem value="answer">{t('datasets.fieldAnswer')}</MenuItem>
                        <MenuItem value="cot">{t('datasets.fieldCOT')}</MenuItem>
                        <MenuItem value="label">{t('datasets.fieldLabel')}</MenuItem>
                      </Select>
                    }
                />
              </Paper>

              <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  onClick={() => setFilterDialogOpen(true)}
                  sx={{ borderRadius: 2, height: 40 }}
              >
                {t('datasets.moreFilters')}
              </Button>
            </Box>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                  variant="contained"
                  startIcon={<FileDownloadIcon />}
                  onClick={handleOpenExportDialog}
                  sx={{ borderRadius: 2 }}
              >
                {t('datasets.export')}
              </Button>
              <Button
                  variant="outlined"
                  startIcon={<FileUploadIcon />}
                  onClick={handleOpenImportDialog}
                  sx={{ borderRadius: 2 }}
              >
                {t('datasets.import.button')}
              </Button>
            </Box>
          </Box>

          {/* 批量操作按钮 */}
          {selectedIds.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<AutoFixHighIcon />}
                    onClick={handleBatchAutoTag}
                    disabled={batchAutoTagLoading}
                    sx={{ borderRadius: 2 }}
                >
                  {batchAutoTagLoading ? '识别中...' : `自动识别领域标签 (${selectedIds.length})`}
                </Button>
                <Button
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleBatchDeleteDataset}
                    sx={{ borderRadius: 2 }}
                >
                  {t('datasets.batchDelete')} ({selectedIds.length})
                </Button>
              </Box>
          )}
        </Box>

        {/* 标签选择弹出框 */}
        <Popover
            open={Boolean(tagPopoverAnchor)}
            anchorEl={tagPopoverAnchor}
            onClose={handleCloseTagPopover}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            PaperProps={{
              sx: {
                width: 340,
                mt: 1.5,
                boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)',
                borderRadius: 1,
                maxHeight: 400
              }
            }}
        >
          <Box sx={{ p: 2 }}>
            <DomainTagFilter
                projectId={projectId}
                onTagSelect={handleTagSelect}
                selectedTag={selectedTag}
            />
          </Box>
        </Popover>

        <DatasetList
            datasets={datasets.data || []}
            onViewDetails={handleViewDetails}
            onDelete={handleOpenDeleteDialog}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
            total={datasets.total}
            selectedIds={selectedIds}
            onSelectAll={handleSelectAll}
            onSelectItem={handleSelectItem}
        />

        <DeleteConfirmDialog
            open={deleteDialog.open}
            datasets={deleteDialog.datasets || []}
            onClose={handleCloseDeleteDialog}
            onConfirm={handleDeleteConfirm}
            batch={deleteDialog.batch}
            progress={deleteProgress}
            deleting={deleteDialog.deleting}
        />

        {/* 更多筛选对话框 */}
        <Dialog open={filterDialogOpen} onClose={() => setFilterDialogOpen(false)} maxWidth="xs" fullWidth>
          <DialogTitle>{t('datasets.filtersTitle')}</DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 3, mt: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('datasets.filterConfirmationStatus')}
              </Typography>
              <Select
                  value={filterConfirmed}
                  onChange={e => setFilterConfirmed(e.target.value)}
                  fullWidth
                  size="small"
                  sx={{ mt: 1 }}
              >
                <MenuItem value="all">{t('datasets.filterAll')}</MenuItem>
                <MenuItem value="confirmed">{t('datasets.filterConfirmed')}</MenuItem>
                <MenuItem value="unconfirmed">{t('datasets.filterUnconfirmed')}</MenuItem>
              </Select>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('datasets.filterCotStatus')}
              </Typography>
              <Select
                  value={filterHasCot}
                  onChange={e => setFilterHasCot(e.target.value)}
                  fullWidth
                  size="small"
                  sx={{ mt: 1 }}
              >
                <MenuItem value="all">{t('datasets.filterAll')}</MenuItem>
                <MenuItem value="yes">{t('datasets.filterHasCot')}</MenuItem>
                <MenuItem value="no">{t('datasets.filterNoCot')}</MenuItem>
              </Select>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
                onClick={() => {
                  setFilterConfirmed('all');
                  setFilterHasCot('all');
                  getDatasetsList();
                }}
            >
              {t('datasets.resetFilters')}
            </Button>
            <Button
                onClick={() => {
                  setFilterDialogOpen(false);
                  setPage(1); // 重置到第一页
                  getDatasetsList();
                }}
                variant="contained"
            >
              {t('datasets.applyFilters')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 导入对话框 */}
        <ImportDatasetDialog
            open={importDialog.open}
            onClose={handleCloseImportDialog}
            projectId={projectId}
            onImportSuccess={handleImportSuccess}
        />

        {/* 导出对话框 */}
        <ExportDatasetDialog
            open={exportDialog.open}
            onClose={handleCloseExportDialog}
            onExport={handleExportDatasets}
            projectId={projectId}
        />
      </Container>
  );
}
