import React, { PureComponent } from 'react';
import { CapsuleChart } from '@jiaminghi/data-view-react';

class UserSituation extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      config: {
        // 单位
        unit: '（条）',
        showValue: false,
        data: [],
      },
    };
  }
  render() {
    const { userIdentityCategory } = this.props;
    console.log("UserIdentityCategory data:", userIdentityCategory)

    const config = {
      unit: '（条）',
      showValue: true,
      data: userIdentityCategory || [],
    };

    console.log("CapsuleChart config:", config);
    return (
      <div>
        {userIdentityCategory && userIdentityCategory.length > 0 ? (
          <CapsuleChart
            config={config}
            style={{
              width: '5.85rem',
              height: '2.625rem',
            }}
          />
        ) : (
          <div style={{
            width: '5.85rem',
            height: '2.625rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#BCDCFF',
            fontSize: '14px'
          }}>
            暂无数据
          </div>
        )}
      </div>
    );
  }
}

export default UserSituation;
