/**
 * LLM响应解析工具函数
 * 用于统一处理LLM返回的各种格式响应
 */

/**
 * 解析LLM返回的JSON响应
 * @param {string|object} response - LLM返回的原始响应
 * @returns {object} 解析后的JSON对象
 */
export function parseLLMResponse(response) {
  try {
    let jsonResult;
    
    if (typeof response === 'string') {
      // 清理响应，移除可能的markdown代码块标记和think标签
      let cleanResponse = response.trim();
      
      // 移除<think>...</think>标签及其内容
      cleanResponse = cleanResponse.replace(/<think>[\s\S]*?<\/think>\s*/g, '');
      
      // 移除markdown代码块标记
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // 去除首尾空白字符
      cleanResponse = cleanResponse.trim();
      
      jsonResult = JSON.parse(cleanResponse);
    } else {
      jsonResult = response;
    }
    
    return {
      success: true,
      data: jsonResult,
      parentTag: jsonResult.parentTag || '',
      childTag: jsonResult.childTag || '',
      reasoning: jsonResult.reasoning || ''
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: null,
      parentTag: '',
      childTag: '',
      reasoning: ''
    };
  }
}

/**
 * 从文本响应中提取标签（降级处理方案）
 * @param {string} response - LLM返回的文本响应
 * @returns {string} 提取的标签
 */
export function extractTagFromText(response) {
  if (typeof response !== 'string') {
    return '未分类';
  }
  
  // 尝试从JSON字符串中提取childTag
  const childTagMatch = response.match(/"childTag"\s*:\s*"([^"]+)"/);
  if (childTagMatch) {
    return childTagMatch[1];
  }
  
  // 尝试匹配格式：数字.数字 标签名
  const lines = response.trim().split('\n');
  for (const line of lines) {
    const trimmedLine = line.trim();
    const match = trimmedLine.match(/^(\d+)\.(\d+)\s+(.+)$/);
    if (match) {
      return `${match[1]}.${match[2]} ${match[3].trim()}`;
    }
  }
  
  return '未分类';
}

/**
 * 检查响应是否包含错误信息
 * @param {string} response - LLM返回的响应
 * @returns {boolean} 是否包含错误信息
 */
export function hasErrorInResponse(response) {
  if (typeof response !== 'string') {
    return false;
  }
  
  return response.includes('模型调用失败') ||
         response.includes('模型调用出错') ||
         response.includes('无有效响应');
}