'use server';

import fs from 'fs';
import path from 'path';
import { getProjectRoot, ensureDir } from '../db/base';
import { getProject } from '@/lib/db/projects';
import { getChunkByProjectId, saveChunks } from '@/lib/db/chunks';
import { downloadDocument } from '@/lib/oss-simple';

// 动态导入 langchain 模块
let TokenTextSplitter, CharacterTextSplitter, RecursiveCharacterTextSplitter, Document;
let markdownSplitter;

async function initializeLangChain() {
  if (!TokenTextSplitter) {
    const textsplitters = await import('@langchain/textsplitters');
    TokenTextSplitter = textsplitters.TokenTextSplitter;
    CharacterTextSplitter = textsplitters.CharacterTextSplitter;
    RecursiveCharacterTextSplitter = textsplitters.RecursiveCharacterTextSplitter;

    const coreDocuments = await import('@langchain/core/documents');
    Document = coreDocuments.Document;

    // 导入Markdown分割工具 (使用require因为是CommonJS模块)
    markdownSplitter = require('./split-markdown/index');
  }
}

async function splitFileByType({ projectPath, fileContent, fileName, projectId, fileId }) {
  // 初始化 LangChain 模块
  await initializeLangChain();

  // 获取任务配置
  const taskConfigPath = path.join(projectPath, 'task-config.json');
  let taskConfig;

  try {
    await fs.promises.access(taskConfigPath);
    const taskConfigData = await fs.promises.readFile(taskConfigPath, 'utf8');
    taskConfig = JSON.parse(taskConfigData);
  } catch (error) {
    // 使用默认配置
    taskConfig = {
      textSplitMinLength: 1500,
      textSplitMaxLength: 2000,
      chunkSize: 1500,
      chunkOverlap: 200,
      separator: '\n\n',
      separators: ['|', '##', '>', '-'],
      splitType: 'default' // 添加默认分割类型
    };
  }
  
  // 获取分割参数
  const minLength = taskConfig.textSplitMinLength || 1500;
  const maxLength = taskConfig.textSplitMaxLength || 2000;
  const chunkSize = taskConfig.chunkSize || 1500;
  const chunkOverlap = taskConfig.chunkOverlap || 200;
  const separator = taskConfig.separator || '\n\n';
  const separators = taskConfig.separators || ['|', '##', '>', '-'];
  const splitLanguage = taskConfig.splitLanguage || 'js';
  const splitType = taskConfig.splitType || 'default'; // 确保有默认值

  if (splitType === 'text') {
    // 字符分块
    const textSplitter = new CharacterTextSplitter({
      separator,
      chunkSize,
      chunkOverlap
    });
    const splitResult = await textSplitter.createDocuments([fileContent]);
    return splitResult.map((part, index) => {
      const chunkId = `${path.basename(fileName, path.extname(fileName))}-part-${index + 1}`;
      return {
        projectId,
        name: chunkId,
        fileId,
        fileName,
        content: part.pageContent,
        summary: '',
        size: part.pageContent.length
      };
    });
  } else if (splitType === 'token') {
    // Token 分块
    const textSplitter = new TokenTextSplitter({
      chunkSize,
      chunkOverlap
    });
    const splitResult = await textSplitter.splitText(fileContent);
    return splitResult.map((part, index) => {
      const chunkId = `${path.basename(fileName, path.extname(fileName))}-part-${index + 1}`;
      return {
        projectId,
        name: chunkId,
        fileId,
        fileName,
        content: part,
        summary: '',
        size: part.length
      };
    });
  } else if (splitType === 'code') {
    // 递归分块
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize,
      chunkOverlap,
      separators
    });
    const jsSplitter = RecursiveCharacterTextSplitter.fromLanguage(splitLanguage, {
      chunkSize,
      chunkOverlap
    });
    const splitResult = await jsSplitter.createDocuments([fileContent]);
    return splitResult.map((part, index) => {
      const chunkId = `${path.basename(fileName, path.extname(fileName))}-part-${index + 1}`;
      return {
        projectId,
        name: chunkId,
        fileId,
        fileName,
        content: part.pageContent,
        summary: '',
        size: part.pageContent.length
      };
    });
  } else if (splitType === 'recursive') {
    // 递归分块
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize,
      chunkOverlap,
      separators
    });
    const splitResult = await textSplitter.splitDocuments([new Document({ pageContent: fileContent })]);
    return splitResult.map((part, index) => {
      const chunkId = `${path.basename(fileName, path.extname(fileName))}-part-${index + 1}`;
      return {
        projectId,
        name: chunkId,
        fileId,
        fileName,
        content: part.pageContent,
        summary: '',
        size: part.pageContent.length
      };
    });
  } else {
    // 默认采用之前的分块方法
    const splitResult = markdownSplitter.splitMarkdown(fileContent, minLength, maxLength);
    return splitResult.map((part, index) => {
      const chunkId = `${path.basename(fileName, path.extname(fileName))}-part-${index + 1}`;
      return {
        projectId,
        name: chunkId,
        fileId,
        fileName,
        content: part.content,
        summary: part.summary,
        size: part.content.length
      };
    });
  }
}

/**
 * 分割项目中的Markdown文件
 * @param {string} projectId - 项目ID
 * @param {Object} file - 文件对象，包含fileName和fileId
 * @returns {Promise<Array>} - 分割结果数组
 */
export async function splitProjectFile(projectId, file) {
  const { fileName, fileId } = file;
  try {
    console.log(`开始分割文件: ${fileName}, fileId: ${fileId}`);
    
    // 初始化 LangChain 模块
    await initializeLangChain();
    console.log('LangChain模块初始化完成');

    // 获取项目根目录（用于保存chunks和toc）
    const projectRoot = await getProjectRoot();
    const projectPath = path.join(projectRoot, projectId);
    console.log(`项目路径: ${projectPath}`);

    // 从OSS读取文件内容
    let fileContent;
    try {
      console.log(`从OSS下载文件: ${fileId}`);
      const fileResult = await downloadDocument(fileId);
      fileContent = fileResult.content;
      console.log(`文件下载成功，内容长度: ${fileContent.length}`);
    } catch (error) {
      console.error(`OSS文件下载失败: ${error.message}`);
      throw new Error(`文件 ${fileName} 不存在或无法从OSS读取: ${error.message}`);
    }

    // 保存分割结果到chunks目录
    console.log('开始执行文本分割...');
    const savedChunks = await splitFileByType({ projectPath, fileContent, fileName, projectId, fileId });
    console.log(`文本分割完成，生成 ${savedChunks.length} 个文本块`);
    
    await saveChunks(savedChunks);
    console.log('文本块保存到数据库完成');

    // 提取目录结构（如果需要所有文件的内容拼接后再提取目录）
    console.log('开始提取目录结构...');
    const tocJSON = markdownSplitter.extractTableOfContents(fileContent);
    const toc = markdownSplitter.tocToMarkdown(tocJSON, { isNested: true });
    console.log('目录结构提取完成');

    // 保存目录结构到单独的toc文件夹
    const tocDir = path.join(projectPath, 'toc');
    await ensureDir(tocDir);
    const tocPath = path.join(tocDir, `${path.basename(fileName, path.extname(fileName))}-toc.json`);
    await fs.promises.writeFile(tocPath, JSON.stringify(tocJSON, null, 2));
    console.log(`目录结构保存到: ${tocPath}`);

    return {
      fileName,
      totalChunks: savedChunks.length,
      chunks: savedChunks,
      toc
    };
  } catch (error) {
    console.error('文本分割出错:', error);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
}

/**
 * 获取项目中的所有文本块
 * @param {string} projectId - 项目ID
 * @returns {Promise<Array>} - 文本块详细信息数组
 */
export async function getProjectChunks(projectId, filter) {
  try {
    // 初始化 LangChain 模块
    await initializeLangChain();

    const projectRoot = await getProjectRoot();
    const projectPath = path.join(projectRoot, projectId);
    const tocDir = path.join(projectPath, 'toc');
    const project = await getProject(projectId);

    let chunks = await getChunkByProjectId(projectId, filter);
    // 读取所有TOC文件
    const tocByFile = {};
    let toc = '';
    try {
      await fs.promises.access(tocDir);
      const tocFiles = await fs.promises.readdir(tocDir);

      for (const tocFile of tocFiles) {
        if (tocFile.endsWith('-toc.json')) {
          const tocPath = path.join(tocDir, tocFile);
          const tocContent = await fs.promises.readFile(tocPath, 'utf8');
          const fileName = tocFile.replace('-toc.json', '.md');

          try {
            tocByFile[fileName] = JSON.parse(tocContent);
            toc += '# File：' + fileName + '\n';
            toc += markdownSplitter.tocToMarkdown(tocByFile[fileName], { isNested: true }) + '\n';
          } catch (e) {
            console.error(`解析TOC文件 ${tocFile} 出错:`, e);
          }
        }
      }
    } catch (error) {
      // TOC目录不存在或读取出错，继续处理
    }
    // 整合结果
    let fileResult = {
      fileName: project.name + '.md',
      totalChunks: chunks.length,
      chunks,
      toc
    };

    return {
      fileResult, // 单个文件结果，而不是数组
      chunks
    };
  } catch (error) {
    console.error('获取文本块出错:', error);
    throw error;
  }
}

/**
 * 获取项目中的所有目录
 * @param {string} projectId - 项目ID
 */
export async function getProjectTocs(projectId) {
  try {
    // 初始化 LangChain 模块
    await initializeLangChain();

    const projectRoot = await getProjectRoot();
    const projectPath = path.join(projectRoot, projectId);
    const tocDir = path.join(projectPath, 'toc');

    // 读取所有TOC文件
    const tocByFile = {};
    let toc = '';
    try {
      await fs.promises.access(tocDir);
      const tocFiles = await fs.promises.readdir(tocDir);

      for (const tocFile of tocFiles) {
        if (tocFile.endsWith('-toc.json')) {
          const tocPath = path.join(tocDir, tocFile);
          const tocContent = await fs.promises.readFile(tocPath, 'utf8');
          const fileName = tocFile.replace('-toc.json', '.md');

          try {
            tocByFile[fileName] = JSON.parse(tocContent);
            toc += '# File：' + fileName + '\n';
            toc += markdownSplitter.tocToMarkdown(tocByFile[fileName], { isNested: true }) + '\n';
          } catch (e) {
            console.error(`解析TOC文件 ${tocFile} 出错:`, e);
          }
        }
      }
    } catch (error) {
      // TOC目录不存在或读取出错，继续处理
    }

    return toc;
  } catch (error) {
    console.error('获取文本块出错:', error);
    throw error;
  }
}

/**
 * 指定文件的目录
 */
export async function getProjectTocByName(projectId, fileName) {
  try {
    // 初始化 LangChain 模块
    await initializeLangChain();

    const projectRoot = await getProjectRoot();
    const projectPath = path.join(projectRoot, projectId);
    const tocDir = path.join(projectPath, 'toc');

    // 读取所有TOC文件
    const tocByFile = {};
    let toc = '';
    try {
      await fs.promises.access(tocDir);
      const tocFiles = await fs.promises.readdir(tocDir);

      for (const tocFile of tocFiles) {
        if (tocFile.endsWith(fileName.replace('.md', '') + '-toc.json')) {
          const tocPath = path.join(tocDir, tocFile);
          const tocContent = await fs.promises.readFile(tocPath, 'utf8');
          const fileName = tocFile.replace('-toc.json', '.md');

          try {
            tocByFile[fileName] = JSON.parse(tocContent);
            toc += '# File：' + fileName + '\n';
            toc += markdownSplitter.tocToMarkdown(tocByFile[fileName], { isNested: true }) + '\n';
          } catch (e) {
            console.error(`解析TOC文件 ${tocFile} 出错:`, e);
          }
        }
      }
    } catch (error) {
      // TOC目录不存在或读取出错，继续处理
    }

    return toc;
  } catch (error) {
    console.error('获取文本块出错:', error);
    throw error;
  }
}
