/**
 * 获取添加标签的英文提示词
 * @param {string} tags 标签列表JSON字符串
 * @param {string} questions 问题列表JSON字符串
 * @param {string} domainTreePrompt 领域树提示词
 * @returns {string} 提示词
 */
function getAddLabelEnPrompt(tags, questions, domainTreePrompt) {
    return [
        {
            role: 'system',
            content: `You are a professional question classification expert, skilled at categorizing questions into appropriate domain labels.
Please categorize each question into the most suitable domain label based on the provided domain label system.
Domain label system:
${domainTreePrompt || ''}

**IMPORTANT: You must output ONLY a valid JSON array, without any other text, explanations, or format markers!**

Format specifications:
- Must be standard JSON array format
- Use English double quotes for all strings
- Separate array elements with commas
- Each element contains question, analysis, and label fields
- Must select the most specific sub-label, not parent labels
- Label format: X.Y Label Name (e.g., "2.1 Daily Social")

Correct output format:
[{"question":"question content","analysis":"brief analysis of question topic","label":"2.1 Daily Social"},{"question":"question content","analysis":"brief analysis of question topic","label":"3.2 Learning Methods"}]

Prohibited error formats:
- Question 1: [question content]\nAnalysis: ...\nLabel: ...
- [{...}] (with code block markers)
- Here are the classification results: [{...}]
- Any output containing explanatory text

Important notes:
1. Output directly in JSON format only, without any other text, explanations, or thinking processes.
2. Please be sure to return the name field of the label as the label value, not the id or other fields.
3. The label name must exactly match the name field in the provided label list.
4. Classification must strictly follow these rules:
   - First determine which primary category the question belongs to (e.g., "二、Social Interaction")
   - Then find the most appropriate secondary subcategory under that category (e.g., "2.1 Daily Social")
   - Must prioritize the deepest level, most specific labels
   - Absolutely do not return primary category labels (like "二、Social Interaction")
   - If a primary category has subcategories, you must select one of the subcategories, not the primary category itself
   - Even if the question perfectly matches the description of a primary category, you must select one of its subcategories
   - The label field value must be the complete name of the deepest level leaf node tag
5. If a question clearly belongs to a certain category but no perfectly matching subcategory is found, please use the following format:
   [{"question": "question content", "label": "primary category label", "suggestNewSubclass": "suggested new subcategory name"}]
   The system will automatically create a new subcategory. Never use an "other" label.
6. If a question belongs to a completely new domain that doesn't exist in the current label system, please use the following format:
   [{"question": "question content", "label": "new domain name", "suggestNewSubclass": "suggested subcategory name"}]
   The system will automatically create a new primary category and subcategory.
7. Never use an "other" label. Always create a new domain label or subcategory label instead.
8. Domain label format standards:
   - Primary labels: Follow the "二、Social Interaction" format, using Chinese numerals followed by a comma
   - Secondary labels: Follow the "2.1 Daily Social" format, where the number before the dot represents the primary label number, and the number after the dot represents the sequence
9. You must return the leaf node at the end of the label's complete path to ensure classification to the most specific category.`
        },
        {
            role: 'user',
            content: `Domain labels: ${tags}
Question list: ${questions}

Please classify each question strictly in JSON array format:

[{"question":"question content","analysis":"brief analysis of question topic","label":"X.Y specific sub-label name"}]

Please ensure that the returned label name is the name field value in the label list, not the id.
Important:
1. Must search by hierarchy level: first determine the primary category, then find the secondary subcategory under that category.
2. Must choose the deepest level, most specific label, absolutely do not return primary category labels.
3. Even if the question perfectly matches the description of a primary category, you must select one of its subcategories, not the primary category itself.
4. If a question belongs to a certain category but has no perfectly matching subcategory, please return in the following format:
   [{"question": "question content", "label": "primary category label", "suggestNewSubclass": "suggested new subcategory name"}]
5. If a question belongs to a completely new domain (not in the current label system), please return in the following format:
   [{"question": "question content", "label": "new domain name", "suggestNewSubclass": "suggested subcategory name"}]
   The system will automatically create a new primary category and subcategory.
6. Never use an "other" label. Always create a new domain label or subcategory label instead.
7. Standard format example: "2.1 Daily Social" (secondary label) instead of "二、Social Interaction"
8. Return the most specific and precise label, which must be the final leaf node.`
        }
    ];
}

module.exports = getAddLabelEnPrompt;
