import { getProjectRoot } from '@/lib/db/base';
import { getTaskConfig } from '@/lib/db/projects';
import { downloadDocument } from '@/lib/oss-simple';
import convertPrompt from '@/lib/llm/prompts/pdfToMarkdown';
import convertPromptEn from '@/lib/llm/prompts/pdfToMarkdownEn';
import reTitlePrompt from '@/lib/llm/prompts/optimalTitle';
import reTitlePromptEn from '@/lib/llm/prompts/optimalTitleEn';
import path from 'path';
import fs from 'fs';

export async function visionProcessing(projectId, fileName, options = {}) {
  try {
    const { updateTask, task, message, fileId } = options;

    if (!fileId) {
      throw new Error('fileId is required for OSS file processing');
    }

    let taskCompletedCount = task.completedCount;

    console.log('executing vision conversion strategy......');

    // 从OSS下载PDF文件
    console.log(`从OSS下载PDF文件: ${fileName}, fileId: ${fileId}`);
    const fileResult = await downloadDocument(fileId);
    
    // 创建临时文件路径
    const tempDir = path.join(process.cwd(), 'temp');
    const tempFilePath = path.join(tempDir, fileName);
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 将PDF内容写入临时文件
    fs.writeFileSync(tempFilePath, Buffer.from(fileResult.content, 'binary'));

    // 获取项目路径
    const projectRoot = await getProjectRoot();
    const projectPath = path.join(projectRoot, projectId);

    // 获取项目配置
    const taskConfig = await getTaskConfig(projectId);

    const model = task.vsionModel;

    if (!model) {
      throw new Error('please check if pdf conversion vision model is configured');
    }

    if (model.type !== 'vision') {
      throw new Error(
        `${model.modelName}(${model.providerName}) this model is not a vision model, please check [model configuration]`
      );
    }

    if (!model.apiKey) {
      throw new Error(
        `${model.modelName}(${model.providerName}) this model has no api key configured, please check [model configuration]`
      );
    }

    const convert = task.language === 'en' ? convertPromptEn : convertPrompt;
    const reTitle = task.language === 'en' ? reTitlePromptEn : reTitlePrompt;

    //创建临时文件夹分割不同任务产生的临时图片文件，防止同时读写一个文件夹，导致内容出错
    const config = {
      pdfPath: tempFilePath, // 使用临时文件路径
      outputDir: path.join(projectPath, 'files'),
      apiKey: model.apiKey,
      model: model.modelId,
      baseUrl: model.endpoint,
      useFullPage: true,
      verbose: false,
      concurrency: taskConfig.visionConcurrencyLimit,
      prompt: convert(),
      textPrompt: reTitle(),
      onProgress: async ({ current, total, taskStatus }) => {
        if (updateTask && task.id) {
          message.current.processedPage = current;
          message.setpInfo = `processing ${fileName} ${current}/${total} pages progress: ${(current / total) * 100}% `;
          await updateTask(task.id, {
            completedCount: taskCompletedCount + current,
            detail: JSON.stringify(message)
          });
        }
      }
    };

    console.log('vision strategy: starting pdf file processing');

    const { parsePdf } = await import('pdf2md-js');
    await parsePdf(tempFilePath, config);

    // 清理临时文件
    if (fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath);
    }

    //转换结束
    return { success: true };
  } catch (error) {
    console.error('vision strategy processing error:', error);
    throw error;
  }
}

export default {
  visionProcessing
};
