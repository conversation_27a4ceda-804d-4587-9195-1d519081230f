import React, { PureComponent } from 'react';
import { LeftPage, LeftTopBox, LeftBottomBox } from './style';
import { ModuleTitle } from '../../style/globalStyledSet';
import { BorderBox12, BorderBox13 } from '@jiaminghi/data-view-react';
import TrafficSituation from './charts/TrafficSituation';
import UserSituation from './charts/UserSituation';

class index extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      realData: null,
      loading: false,
      error: null
    };
  }

  // 使用缓存服务获取真实数据
  async fetchRealData(projectId = null, selectedCountry = null) {
    if (!this.props.dataCacheService) {
      console.warn('数据缓存服务不可用');
      this.setState({
        realData: this.getEmptyData(),
        loading: false
      });
      return null;
    }

    try {
      this.setState({ loading: true, error: null });

      // 使用缓存服务获取文件记录统计数据
      const fileStats = await this.props.dataCacheService.fetchFileRecordsData(projectId, selectedCountry);

      // 获取最新的数据集用于表格显示
      let userSitua;
      if (projectId) {
        // 获取特定项目的数据集（使用缓存）
        try {
          const datasets = await this.props.dataCacheService.fetchProjectDatasets(projectId, true);
          userSitua = {
            header: ['问题', '答案', '时间'],
            data: datasets.slice(0, 10).map(item => [
              item.question || '',
              item.answer || '',
              item.createAt ? new Date(item.createAt).toLocaleDateString('zh-CN') : ''
            ])
          };
        } catch (error) {
          console.warn(`获取项目 ${projectId} 数据集失败:`, error);
          userSitua = this.getEmptyUserSitua();
        }
      } else {
        // 获取所有项目的最新数据集
        userSitua = await this.fetchAllProjectsData();
      }

      const realData = {
        accessFrequency: fileStats.avgDailyProcessing || 0, // 平均处理数据(天) - 已经是MB，保留3位小数
        peakFlow: fileStats.peakFlow || 0, // 流量峰值 - 已经是MB，保留3位小数
        trafficSitua: {
          timeList: fileStats.timeList || this.generateMonthlyTimeList(),
          inData: fileStats.inData || Array(6).fill(0), // 入库数据 - 已经是MB，保留3位小数
          outData: fileStats.outData || Array(6).fill(0) // 出库数据 - 已经是MB，保留3位小数
        },
        userSitua: userSitua || this.getEmptyUserSitua()
      };

      this.setState({ realData, loading: false });
      return realData;
    } catch (error) {
      console.error('获取真实数据失败:', error);
      this.setState({ error: error.message, loading: false });
      return null;
    }
  }

  // 英文国家名转换为国家代码
  getCountryCode(countryName) {
    const countryNameToCode = {
      'Brunei': 'BN',
      'Cambodia': 'KH',
      'Indonesia': 'ID',
      'Laos': 'LA',
      'Malaysia': 'MY',
      'Myanmar': 'MM',
      'Philippines': 'PH',
      'Singapore': 'SG',
      'Thailand': 'TH',
      'Vietnam': 'VN'
    };
    return countryNameToCode[countryName];
  }

  // 获取所有项目的最新数据集（优先使用缓存）
  async fetchAllProjectsData() {
    try {
      if (!this.props.dataCacheService) {
        return this.getEmptyUserSitua();
      }

      // 使用缓存服务获取所有项目的已确认数据集
      const { allDatasets } = await this.props.dataCacheService.fetchAllProjectsData(true);

      if (allDatasets.length === 0) {
        return this.getEmptyUserSitua();
      }

      // 按时间排序，获取最新的数据
      allDatasets.sort((a, b) => new Date(b.createAt) - new Date(a.createAt));

      return {
        header: ['问题', '答案', '时间'],
        data: allDatasets.slice(0, 10).map(item => [
          item.question || '',
          item.answer || '',
          item.createAt ? new Date(item.createAt).toLocaleDateString('zh-CN') : ''
        ])
      };
    } catch (error) {
      console.warn('获取所有项目数据失败:', error);
      return this.getEmptyUserSitua();
    }
  }

  // 获取空的用户情况数据
  getEmptyUserSitua() {
    return {
      header: ['问题', '答案', '时间'],
      data: []
    };
  }

  // 获取空数据
  getEmptyData() {
    return {
      accessFrequency: 0,
      peakFlow: 0,
      trafficSitua: {
        timeList: this.generateMonthlyTimeList(),
        inData: Array(6).fill(0),
        outData: Array(6).fill(0)
      },
      userSitua: this.getEmptyUserSitua()
    };
  }

  // 生成本月日期
  generateMonthlyTimeList() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    // 获取本月的天数
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // 将本月分为6个时间节点
    const timeList = [];
    for (let i = 0; i < 6; i++) {
      // 计算每个节点对应的日期（均匀分布）
      const dayOfMonth = Math.round((daysInMonth / 5) * i) + 1;
      // 确保不超过本月最大天数
      const actualDay = Math.min(dayOfMonth, daysInMonth);

      const monthStr = String(month + 1).padStart(2, '0');
      const dayStr = String(actualDay).padStart(2, '0');
      timeList.push(`${monthStr}/${dayStr}`);
    }
    return timeList;
  }



  // 组件挂载时获取数据
  componentDidMount() {
    const { selectedCountry, countryProjectMap } = this.props;
    if (selectedCountry && countryProjectMap) {
      const projectId = countryProjectMap[selectedCountry];
      if (projectId) {
        this.fetchRealData(projectId, selectedCountry);
      } else {
        // 选中国家但没有对应项目ID，尝试根据国家获取数据
        this.fetchRealData(null, selectedCountry);
      }
    } else {
      // 没有选中国家时，获取全局数据
      this.fetchRealData();
    }
  }

  // 当选中国家变化时获取数据
  componentDidUpdate(prevProps) {
    const { selectedCountry, countryProjectMap } = this.props;
    if (selectedCountry !== prevProps.selectedCountry) {
      if (selectedCountry && countryProjectMap) {
        const projectId = countryProjectMap[selectedCountry];
        if (projectId) {
          this.fetchRealData(projectId, selectedCountry);
        } else {
          // 选中国家但没有对应项目ID，尝试根据国家获取数据
          this.fetchRealData(null, selectedCountry);
        }
      } else {
        // 没有选中国家时，获取全局数据
        this.fetchRealData();
      }
    }
  }

  // 根据选中国家获取数据
  getDataByCountry = () => {
    const { realData, loading, error } = this.state;

    // 如果正在加载，显示加载状态
    if (loading) {
      return {
        accessFrequency: 0,
        peakFlow: 0,
        trafficSitua: {
          timeList: this.generateMonthlyTimeList(),
          outData: [0, 0, 0, 0, 0, 0],
          inData: [0, 0, 0, 0, 0, 0],
        },
        userSitua: {
          header: ['问题', '答案', '时间'],
          data: [['加载中...', '', '']],
        },
      };
    }

    // 如果有错误，显示错误信息
    if (error) {
      return {
        accessFrequency: 0,
        peakFlow: 0,
        trafficSitua: {
          timeList: this.generateMonthlyTimeList(),
          outData: [0, 0, 0, 0, 0, 0],
          inData: [0, 0, 0, 0, 0, 0],
        },
        userSitua: {
          header: ['问题', '答案', '时间'],
          data: [['数据加载失败', error, '']],
        },
      };
    }

    // 如果有真实数据，使用真实数据
    if (realData) {
      return realData;
    }

    // 默认返回空数据
    return this.getEmptyData();


  }

  render() {
    const { selectedCountry } = this.props;
    const currentData = this.getDataByCountry();
    const { userSitua, trafficSitua, accessFrequency, peakFlow } = currentData;
    return (
      <LeftPage>
        {/* 顶部图表 */}
        <LeftTopBox>
          <BorderBox12 className='left-top-borderBox12'>
            <div className='left-top'>
              <ModuleTitle>
                <i className='iconfont'>&#xe78f;</i>
                <span>本月数据态势</span>
              </ModuleTitle>
              <div className='title-dis'>
                <span>
                  平均处理数据(天):
                  <span className='title-dis-keyword'>{accessFrequency}M</span>
                </span>
                <span>
                  流量峰值:
                  <span className='title-dis-keyword'>{peakFlow}M</span>
                </span>
              </div>
              {/* 图表 */}
              <TrafficSituation trafficSitua={trafficSitua}></TrafficSituation>
            </div>
          </BorderBox12>
        </LeftTopBox>

        {/* 底部图表 */}
        <LeftBottomBox>
          <BorderBox13 className='left-bottom-borderBox13'>
            <div className='left-bottom'>
              <ModuleTitle>
                <i className='iconfont'>&#xe88e;</i>
                <span>最新项目数据</span>
              </ModuleTitle>
              {/* 图表 */}
              <UserSituation userSitua={userSitua}></UserSituation>
            </div>
          </BorderBox13>
        </LeftBottomBox>
      </LeftPage>
    );
  }
}

export default index;
