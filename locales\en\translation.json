{"language": {"switchToEnglish": "Switch to English", "switchToChinese": "Switch to Chinese", "en": "EN", "zh": "中"}, "theme": {"switchToLight": "Switch to Light Mode", "switchToDark": "Switch to Dark Mode"}, "settings": {"promptConfig": "Prompt Configuration", "promptsDescription": "Configure prompt used in the project, supporting global prompts and scenario-specific prompts.", "globalPrompt": "Global Prompt", "questionPrompt": "Question Generation Prompt", "answerPrompt": "Answer Generation Prompt", "labelPrompt": "Question Labeling Prompt", "domainTreePrompt": "Domain Tree Building Prompt", "globalPromptPlaceholder": "Enter global prompt that will serve as the base prompt for all scenarios", "questionPromptPlaceholder": "Enter prompt for generating questions", "answerPromptPlaceholder": "Enter prompt for generating answers", "labelPromptPlaceholder": "Enter prompt for question labeling (not supported currently)", "domainTreePromptPlaceholder": "Enter prompt for building domain tree", "loadPromptsFailed": "Failed to load prompt configurations", "savePromptsSuccess": "Successfully saved prompt configurations", "savePromptsFailed": "Failed to save prompt configurations", "title": "Settings", "basicInfo": "Basic Info", "modelConfig": "Model Configuration", "taskConfig": "Task Configuration", "tabsAriaLabel": "Settings Tabs", "idNotEditable": "Project ID is not editable", "saveBasicInfo": "Save Basic Info", "saveSuccess": "Save Successful", "saveFailed": "Save Failed", "deleteSuccess": "Delete Successful", "deleteFailed": "Delete Failed", "fetchTasksFailed": "Failed to fetch task settings", "saveTasksFailed": "Failed to save task settings", "textSplitSettings": "Text Split Settings", "minLength": "Minimum Length", "maxLength": "Maximum Split Length", "textSplitDescription": "Adjust the text split length range", "splitType": "Split Strategy", "splitTypeMarkdown": "Document Structure Spliting (Markdown)", "splitTypeMarkdownDesc": "Automatically split the text according to the titles in the document, maintaining semantic integrity. It is suitable for Markdown documents with a clear structure.", "splitTypeRecursive": "Text Structure Spliting (Custom Delimiter)", "splitTypeRecursiveDesc": "Recursively attempt multiple levels of delimiters (configurable). First, use delimiters with higher priority, and then use secondary delimiters. It is suitable for complex documents.", "splitTypeText": "Fixed-length Spliting (Characters)", "splitTypeTextDesc": "Split the text according to the specified delimiter (configurable), and then combine it according to the specified length. It is suitable for ordinary text files.", "splitTypeToken": "Fixed-length Spliting (Tokens)", "splitTypeTokenDesc": "Block based on the number of Tokens (not the number of characters).", "splitTypeCode": "Intelligent Spliting of Program Code", "splitTypeCodeDesc": "Intelligently block according to the syntax structure of different programming languages, avoiding splitting at places with incomplete syntax.", "codeLanguage": "Programming Language", "codeLanguageHelper": "Select the programming language for smarter code splitting based on language syntax.", "chunkSize": "Chunk Size", "chunkOverlap": "<PERSON><PERSON>", "separator": "Separator", "separatorHelper": "Separator used for splitting text, e.g. \n\n for blank lines", "separators": "Separators List", "separatorsInput": "Separators (comma separated)", "separatorsHelper": "Comma-separated list of separators in priority order", "questionGenSettings": "Question Generation Settings", "questionGenLength": "Question Generation Length: {{length}}", "questionMaskRemovingProbability": "Removing Question Marks Probability: {{probability}}%", "questionGenDescription": "Set the maximum length for generated questions", "huggingfaceSettings": "Hugging Face Settings", "datasetUpload": "Dataset Upload Settings", "huggingfaceToken": "Hugging Face <PERSON>", "huggingfaceNotImplemented": "Hugging Face functionality is not yet implemented", "concurrencyLimit": "Concurrency Limit", "concurrencyLimitHelper": "Limit the number of tasks for generating questions and generating datasets simultaneously. ", "saveTaskConfig": "Save Task Config", "pdfSettings": "PDF file conversion configuration", "minerUToken": "MinerU Token configuration", "minerUHelper": "MinerU Token is valid for only 14 days. Please replace the Token in time", "vision": "Custom large-scale vision model configuration", "visionConcurrencyLimit": "Concurrency limit for custom large-scale vision models"}, "questions": {"autoGenerateDataset": "Auto Generate Dataset", "autoGenerateDatasetTip": "Create background batch processing tasks: automatically query text blocks pending question generation and extract questions.", "filterAll": "All Questions", "filterAnswered": "With Answers", "filterUnanswered": "Without Answers", "title": "Questions", "confirmDeleteTitle": "Confirm Delete Question", "confirmDeleteContent": "Are you sure you want to delete the question \"{{question}}\"? This action cannot be undone.", "deleting": "Deleting question...", "batchDeleteTitle": "Confirm Batch Delete", "batchDeleting": "Deleting {{count}} questions...", "deleteSuccess": "Question deleted successfully", "deleteFailed": "Failed to delete question", "batchDeleteSuccess": "Successfully deleted {{count}} questions", "batchDeletePartial": "Delete completed, success: {{success}}, failed: {{failed}}", "batchDeleteFailed": "Failed to batch delete questions", "noQuestionsSelected": "Please select questions first", "batchGenerateStart": "Starting to generate datasets for {{count}} questions", "invalidQuestionKey": "Invalid question key", "listView": "List View", "treeView": "Domain Tree View", "selectAll": "Select All", "selectedCount": "Selected {{count}} questions", "totalCount": "Total {{count}} questions", "searchPlaceholder": "Search questions or tags...", "deleteSelected": "Delete Selected", "batchGenerate": "Batch Generate Datasets", "generating": "Generating Dataset", "generatingProgress": "Completed: {{completed}}/{{total}}", "generatedCount": "Generated: {{count}}", "pleaseWait": "Please wait, processing...", "createSuccess": "Question created successfully", "updateSuccess": "Question updated successfully", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "editQuestion": "Edit Question", "questionContent": "Question Content", "selectChunk": "Select Text Chunk", "selectTag": "Select Tag", "searchChunk": "Search text chunk", "searchTag": "Search tag", "createQuestion": "Create Question", "questionPlaceholder": "Please enter your question", "noChunkSelected": "Please select a text chunk first", "noTagSelected": "Please select a tag", "deleteConfirm": "Are you sure you want to delete this question? This action cannot be undone."}, "common": {"help": "Help", "jumpTo": "Jump To", "unknownError": "Unknown Error", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "complete": "Complete", "close": "Close", "add": "Add", "remove": "Remove", "loading": "Loading...", "yes": "Yes", "no": "No", "confirmDelete": "Confirm Delete", "saving": "Saving...", "deleting": "Deleting...", "actions": "Actions", "confirmDeleteDataSet": "Are you sure you want to delete this dataset? This action cannot be undone.", "noData": "None", "failed": "Failed", "success": "Success", "backToList": "Back to List", "label": "Label", "confirmDeleteDescription": "Are you sure you want to delete this File? This action cannot be undone.", "more": "More", "fetchError": "Fetching data failed", "confirmDeleteQuestion": "Are you sure you want to delete this question? This action cannot be undone.", "deleteSuccess": "Delete successful", "visitGitHub": "Visit GitHub Repository", "syncOldData": "Sync Old Data", "copy": "Copy", "enabled": "Enabled", "disabled": "Disabled", "copied": "<PERSON>pied", "generating": "Generating...", "required": "Required", "search": "Search", "all": "All", "noResults": "No Results", "noSelected": "None Selected", "language": "en"}, "home": {"title": "Easy Dataset", "subtitle": "A powerful tool for creating fine-tuning datasets for Large Language Models", "createProject": "Create Project", "searchDataset": "Search Public Datasets", "dataScreen": "Data Screen"}, "projects": {"reuseConfig": "Reuse Model Config", "noReuse": "No Configuration Reuse", "selectProject": "Select Project", "fetchFailed": "Failed to fetch project list", "fetchError": "Error fetching project list", "loading": "Loading your projects...", "createFailed": "Failed to create project", "createError": "Error creating project", "createNew": "Create New Project", "saveFailed": "Failed to save project", "id": "Project ID", "name": "Project Name", "description": "Project Description", "country": "Country/Region", "selectCountry": "Please select a country or region", "questions": "Questions", "datasets": "Datasets", "lastUpdated": "Last Updated", "viewDetails": "View Details", "createFirst": "Please create a project first", "noProjects": "No projects found", "notExist": "The project does not exist.", "createProject": "Create Project", "deleteConfirm": "Are you sure you want to delete this project? This action cannot be undone.", "deleteSuccess": "Project deleted successfully", "deleteFailed": "Failed to delete project", "backToHome": "Back to Home", "deleteConfirmTitle": "Confirm Delete"}, "textSplit": {"dragToUpload": "Drag files to upload", "fileList": "File List", "autoGenerateQuestions": "Auto Generate", "autoGenerateQuestionsTip": "Create background batch processing tasks: automatically query text blocks pending question generation and extract questions.", "exportChunks": "Export Chunks", "allChunks": "All Text Chunks", "generatedQuestions2": "With Questions", "ungeneratedQuestions": "Without Questions", "noFilesUploaded": "No files uploaded yet", "unknownFile": "Unknown File", "fetchFilesFailed": "Failed to fetch files", "editTag": "Edit Tag", "deleteTag": "Delete Tag", "addTag": "Add Tag", "selectedCount": "Selected {{count}} text chunks", "totalCount": "total {{count}} text chunks", "batchGenerateQuestions": "Batch Generate", "uploadedDocuments": "Uploaded {{count}} Documents", "title": "Texts", "uploadNewDocument": "Upload New Document", "selectFile": "Select File", "markdownOnly": "Currently only supports Markdown (.md) format files", "supportedFormats": "Supported formats: .pdf .md, .txt, .docx", "uploadAndProcess": "Upload and Process", "selectedFiles": "Selected Files ({{count}})", "oneFileMessage": "File upload not allowed, please delete the existing file first", "mutilFileMessage": "After uploading a new file, the domain tree will be rebuilt", "noChunks": " No text chunks found", "chunkDetails": "Chunk Details: {{chunkId}}", "fetchChunksFailed": "Failed to fetch text chunks", "fetchChunksError": "Error fetching text chunks", "fileResultReceived": "File result received", "fileUploadSuccess": "File uploaded successfully", "splitTextFailed": "Text splitting failed", "splitTextError": "Error splitting text", "deleteChunkFailed": "Failed to delete text chunk", "deleteChunkError": "Error deleting text chunk", "selectModelFirst": "Please select a model first, you can select from the top navigation bar", "modelNotAvailable": "Selected model is not available, please select again", "generateQuestionsFailed": "Failed to generate questions for chunk {{chunkId}}", "questionsGenerated": "{{total}} questions generated", "customSplitMode": "Custom Split Mode", "customSplitInstructions": "Select text to add split points. The system will place split markers at your selected positions.", "splitPointsList": "Added Split Points", "saveSplitPoints": "Save Split Points", "confirmCustomSplitTitle": "Confirm Split Replacement", "confirmCustomSplitMessage": "Note: Custom split points will replace the previously automated split results for this document. Do you want to continue?", "customSplitSuccess": "Custom split saved successfully", "customSplitFailed": "Failed to save custom split", "missingRequiredData": "Missing required data", "chunksPreview": "Chunks Size Preview", "chunk": "Chunk", "characters": " chars", "questionsGeneratedSuccess": "Successfully generated {{total}} questions for the text chunk", "generateQuestionsForChunkFailed": "Failed to generate questions for chunk {{chunkId}}", "generateQuestionsForChunkError": "Error generating questions for chunk {{chunkId}}", "generateQuestionsError": "Error generating questions", "partialSuccess": "Partially successful question generation ({{successCount}}/{{total}}), {{errorCount}} chunks failed", "allSuccess": "Successfully generated {{totalQuestions}} questions for {{successCount}} text chunks", "fileDeleted": "File {{fileName}} deleted, refreshing text chunk list", "tabs": {"smartSplit": "Smart Split", "domainAnalysis": "Domain Analysis"}, "loading": "Loading...", "fetchingDocuments": "Fetching document data", "processing": "Processing...", "progressStatus": "Selected {{total}} text chunks, {{completed}} completed", "processingPleaseWait": "Processing, please wait!", "oneFileLimit": "File upload not allowed, there is already an uploaded file", "unsupportedFormat": "Unsupported file format: {{files}}", "modelInfoParseError": "Failed to parse model information", "uploadFailed": "Upload failed", "uploadSuccess": "Successfully uploaded {{count}} files", "deleteFailed": "Failed to delete file", "deleteSuccess": "File {{fileName}} has been successfully deleted", "generatedQuestions": "{{count}} Questions", "viewDetails": "View Details", "generateQuestions": "Generate Questions", "charsCount": "Characters", "pdfProcessStatus": "Total {{total}} files, {{completed}} have been converted", "pdfPageProcessStatus": "Processing {{fileName}}, {{completed}} out of {{total}} pages converted", "pdfProcessing": "Converting file...", "pdfProcessingFailed": "File conversion failed!", "pdfProcess": "File detected!", "selectPdfProcessingStrategy": "Please select the file processing method:", "pdfProcessingStrategyDefault": "<PERSON><PERSON><PERSON>", "pdfProcessingStrategyDefaultHelper": "Use the built-in PDF parsing strategy", "pdfProcessingStrategyMinerUHelper": "Use MinerU API for parsing. Please configure the MinerU API Token first", "pdfProcessingStrategyVision": "Custom Vision Model", "pdfProcessingStrategyVisionHelper": "Use a custom vision model for parsing", "pdfProcessingToast": "File detected. The system will create a background task to parse the file", "pdfProcessingWaring": "There is a file processing task in progress. It is recommended to wait for the task to complete before performing other operations, otherwise it may affect the quality of data generation!", "pdfProcessingLoading": "Executing file conversion task, please wait for the task to complete before uploading new files...", "basicPdfParsing": "Basic PDF Parsing", "basicPdfParsingDesc": "Capable of identifying the key outlines of simple PDF files with high speed", "mineruApiDesc": "Capable of identifying complex PDF files, including formulas and charts (Requires configuration of MinerU API Key)", "mineruApiDescDisabled": "Please go to Project Settings - Task Configuration to set up MinerU Token", "mineruWebPlatform": "MinerU Online Platform Parsing", "mineruWebPlatformDesc": "Capable of identifying complex PDF files, including formulas and charts (Requires redirecting to another website)", "mineruSelected": "Selected to use MinerU for parsing PDFs", "customVisionModel": "Custom Vision Model Parsing", "customVisionModelDesc": "Capable of identifying complex PDF files, including formulas and charts (Requires adding vision model configuration to the model configuration)", "customVisionModelSelected": "Selected to use the visual large model {{name}} ({{provider}}) for parsing PDFs", "defaultSelected": "Selected to use the default built-in strategy for parsing PDFs", "download": "Download the document", "deleteFile": "Delete document", "viewChunk": "View Text Chunk", "editChunk": "Edit Text Chunk {{chunkId}}", "editChunkSuccess": "Text chunk edited successfully", "editChunkFailed": "Failed to edit text chunk", "editChunkError": "Error occurred when editing text chunk", "deleteFileWarning": "Warning: Deleting this document will also delete the following related items", "deleteFileWarningChunks": "All associated text chunks", "deleteFileWarningQuestions": "All questions generated from these chunks", "deleteFileWarningDatasets": "All datasets created from these questions", "domainTree": {"firstUploadTitle": "Domain Tree Generation", "uploadTitle": "Document Upload - Domain Tree Processing", "deleteTitle": "Document Deletion - Domain Tree Processing", "reviseOption": "Revise Domain Tree", "reviseDesc": "Modify the current domain tree based on added or deleted documents, only affecting changed parts", "rebuildOption": "Rebuild Domain Tree", "rebuildDesc": "Generate a completely new domain tree based on all document contents", "keepOption": "Keep Unchanged", "keepDesc": "Keep the current domain tree structure unchanged without any modifications"}}, "domain": {"title": "Domain Knowledge Tree", "addRootTag": "Add Root Tag", "addFirstTag": "Add First Tag", "noTags": "No domain tags available", "docStructure": "Document Structure", "noToc": "No table of contents available. Please upload and process the document first.", "editTag": "Edit Tag", "editQuestion": "Edit Question", "deleteTag": "Delete Tag", "addChildTag": "Add Child Tag", "deleteTagConfirmTitle": "Delete Tag", "deleteTagConfirmMessage": "Are you sure you want to delete tag \"{{tag}}\"?", "deleteWarning": "This action will delete this tag and all its child tags, questions, and datasets. This cannot be undone!", "tagName": "Tag Name", "tagNameRequired": "Tag name cannot be empty", "editTagError": "Failed to edit tag", "inputEditTagName": "Please enter new tag name", "originalTagName": "Original Tag Name", "questionContent": "Question Content", "questionRequired": "Question content cannot be empty", "tagRequired": "Please select at least one tag", "editQuestionError": "Failed to edit question", "inputEditQuestionContent": "Please enter new question content", "originalQuestion": "Original Question", "originalTags": "Original Tags", "selectTags": "Select Tags", "selectTagsPlaceholder": "Please select tags for the question", "dialog": {"addTitle": "Add Tag", "editTitle": "Edit Tag", "addChildTitle": "Add Child Tag", "inputRoot": "Please enter a new root tag name", "inputEdit": "Please edit the tag name", "inputChild": "Please add a child tag for \"{label}\"", "labelName": "Tag Name", "saving": "Saving...", "save": "Save", "deleteConfirm": "Are you sure to delete tag \"{label}\"?", "deleteWarning": "This action will delete all child tags and cannot be undone.", "emptyLabel": "Tag name cannot be empty"}, "tabs": {"tree": "Domain Tree", "structure": "Document Structure"}, "errors": {"saveFailed": "Failed to save tags"}, "messages": {"updateSuccess": "Tags updated successfully"}}, "export": {"title": "Export Dataset", "format": "Format", "fileFormat": "File Format", "systemPrompt": "System Prompt", "systemPromptPlaceholder": "Please enter system prompt...", "onlyConfirmed": "Only export confirmed data", "example": "Format Example", "confirmExport": "Confirm Export", "includeCOT": "Include Chain of Thought", "cotDescription": "Includes the reasoning process before the final answer", "customFormat": "Custom Format", "customFormatSettings": "Custom Format Settings", "questionFieldName": "Question Field Name", "answerFieldName": "Answer Field Name", "cotFieldName": "Cot Field Name", "includeLabels": "Include Labels", "includeChunk": "Include Chunk", "localTab": "Local Export", "llamaFactoryTab": "Llama Factory", "huggingFaceTab": "HuggingFace", "configExists": "Configuration File Exists", "configPath": "Configuration File Path", "updateConfig": "Update LLaMA Factory Configuration", "noConfig": "No configuration file exists, click the button below to generate", "generateConfig": "Generate LLaMA Factory Configuration", "huggingFaceComingSoon": "HuggingFace export feature coming soon", "uploadToHuggingFace": "Upload to HuggingFace", "datasetName": "Dataset Name", "datasetNameHelp": "Format: username/dataset-name", "privateDataset": "Private Dataset", "datasetSettings": "Dataset Settings", "exportOptions": "Export Options", "uploadSuccess": "Dataset uploaded successfully to HuggingFace", "viewOnHuggingFace": "View on HuggingFace", "noTokenWarning": "Hugging Face Token not found. Please configure it in project settings.", "goToSettings": "Go to Settings", "tokenHelp": "You can get your token from HuggingFace settings page"}, "datasets": {"selectDomainTag": "Select Domain Tag", "import": {"button": "Import Dataset", "title": "Import Dataset", "description": "Import dataset from file", "formatDescription": "Please ensure the file has the correct format:", "columnB": "Column B", "columnC": "Column C", "columnD": "Column D", "columnE": "Column E", "dragAndDrop": "Drag file here", "or": "or", "browseFiles": "Browse Files", "supportedFormats": "Supported formats", "invalidFileType": "Invalid file type, please select the correct file format", "noFileSelected": "Please select a file first", "uploading": "Uploading", "importing": "Importing...", "import": "Import", "partialSuccess": "Partially imported successfully", "errors": "errors", "unknownError": "Unknown error occurred during import", "selectColumns": "Select columns to import", "columnRequired": "Required", "jsonFormat": "JSON/JSONL/CSV files should contain the following fields:", "jsonImportNote": "Will import question, answer, domain tag and chain of thought fields (if present) from the file", "formatSelection": "Format Selection", "formatType": "Data Format", "autoDetect": "Auto Detect", "customFormat": "Custom Format", "fieldMapping": "Field Mapping", "supportedFields": "Supported field names", "questionFields": "Question fields", "answerFields": "Answer fields", "tagFields": "Tag fields", "cotFields": "Chain of thought fields", "autoDetectDomainTag": "Auto-detect domain tags", "autoDetectDomainTagHelp": "Use AI to automatically identify domain tags for questions", "autoDetectRequiresDomainTag": "Auto-detect domain tags requires domain tag import to be enabled", "tagStrategy1": "If domain tag is checked, it will use tags from the imported data", "tagStrategy2": "If domain tag is not checked, it will be set to 'Other'", "tagStrategy3": "If both domain tag and auto-detect are checked, it will only detect empty tags", "tagStrategy4": "If only auto-detect is checked, it will detect all tags"}}, "update": {"newVersion": "New Version", "newVersionAvailable": "New Version Available", "currentVersion": "Current Version", "latestVersion": "Latest Version", "downloadNow": "Download Now", "downloading": "Downloading", "installNow": "Install Now", "updating": "Updating...", "updateNow": "Update Now", "viewRelease": "View Release Notes", "checking": "Checking for updates...", "noUpdates": "Already up to date", "updateError": "Update Error", "updateSuccess": "Update Successful", "restartRequired": "<PERSON><PERSON> Required", "restartNow": "Restart Now", "restartLater": "<PERSON><PERSON>"}, "datasetSquare": {"title": "Dataset Square", "subtitle": "Discover and explore various public dataset resources to power your model training and research", "searchPlaceholder": "Search dataset keywords...", "searchVia": "via", "categoryTitle": "Dataset Categories", "categories": {"all": "All", "popular": "Popular", "chinese": "Chinese Resources", "english": "English Resources", "research": "Research Data", "multimodal": "Multimodal"}, "foundResources": "Found {{count}} dataset resources", "currentFilter": "Current filter: {{category}}", "noDatasets": "No datasets found matching your criteria", "tryOtherCategories": "Try other categories or return to all datasets", "dataset": "Dataset", "viewDataset": "View Dataset", "domainTag": "Domain Tags", "allTags": "All Tags"}, "playground": {"title": "Model Testing", "selectModelFirst": "Please select a model", "sendFirstMessage": "Send your first message to start testing", "inputMessage": "Enter message...", "send": "Send", "outputMode": "Output Mode", "normalOutput": "Normal Output", "streamingOutput": "Streaming Output", "clearConversation": "Clear Conversation", "selectModelMax3": "Please select up to 3 models to test", "reasoningProcess": "Reasoning Chain"}, "chunks": {"title": "Text Chunk", "defaultTitle": "Default Title"}, "documentation": "Documentation", "models": {"configNotFound": "Model config not found", "parseError": "Failed to parse model config", "fetchFailed": "Failed to fetch model", "saveFailed": "Failed to save model config", "pleaseSelectModel": "Please select at least one model", "title": "Model Settings", "add": "Add Model", "unselectedModel": "Unselected Model", "unconfiguredAPIKey": "Unconfigured API Key", "saveAllModels": "Save All Models", "edit": "Edit", "delete": "Delete", "modelName": "Model Name", "endpoint": "Endpoint", "apiKey": "API Key", "provider": "Provider", "localModel": "Local Model", "apiKeyConfigured": "API Key Configured", "apiKeyNotConfigured": "API Key Not Configured", "temperature": "Temperature", "maxTokens": "<PERSON>", "type": "Model Type", "text": "Large Language Model", "vision": "Vision Large Model", "typeTips": "If you want to use a custom vision model to parse PDFs, please configure at least one vision large model", "refresh": "Refresh Models"}, "stats": {"ongoingProjects": "Ongoing Projects", "questionCount": "Question Count", "generatedDatasets": "Generated Datasets", "supportedModels": "Supported Models"}, "migration": {"title": "Project Migration", "description": "Some projects need to be migrated to the database. Migration can improve performance and support more features.", "projectsList": "Unmigrated Projects", "migrate": "Start Migration", "migrating": "Migrating...", "success": "Successfully migrated {{count}} projects", "failed": "Migration failed", "checkFailed": "Failed to check unmigrated projects", "checkError": "Error checking unmigrated projects", "starting": "Starting migration task...", "processing": "Processing migration task...", "completed": "Migration completed", "startFailed": "Failed to start migration task", "statusFailed": "Failed to get migration status", "taskNotFound": "Migration task not found", "progressStatus": "Migrated {{completed}}/{{total}} projects", "openDirectory": "Open Directory", "deleteDirectory": "Delete Directory", "confirmDelete": "Are you sure you want to delete this project directory? This action cannot be undone.", "openDirectoryFailed": "Failed to open project directory", "deleteDirectoryFailed": "Failed to delete project directory"}, "distill": {"title": "Distill", "generateRootTags": "Generate Root Tags", "generateSubTags": "Generate Sub Tags", "generateQuestions": "Generate Questions", "generateRootTagsTitle": "Generate Root Domain Tags", "generateSubTagsTitle": "Generate Sub Tags for {{parentTag}}", "generateQuestionsTitle": "Generate Questions for {{tag}}", "parentTag": "Parent Tag", "parentTagPlaceholder": "Enter parent tag name (e.g., Sports, Technology)", "parentTagHelp": "Enter a domain topic, and the system will generate related tags based on it", "generateQuestionsError": "Failed to generate questions", "tagCount": "Number of Tags", "tagCountHelp": "Enter the number of tags to generate, maximum is 100", "questionCount": "Number of Questions", "questionCountHelp": "Enter the number of questions to generate, maximum is 100", "generatedTags": "Generated Tags", "generatedQuestions": "Generated Questions", "tagPath": "Tag Path", "noTags": "No Tags", "noQuestions": "No Questions", "clickGenerateButton": "Click the generate button above to create tags", "selectModelFirst": "Please select a model first", "selectModel": "Select Model", "generateTagsError": "Failed to generate tags", "generateTags": "Generate Tags", "subTags": "sub-tags", "questions": "questions", "deleteTagConfirmTitle": "Confirm to delete tag?", "unknownTag": "Unknown Tag", "autoDistillButton": "Auto Distill Dataset", "autoDistillTitle": "Automated Dataset Distillation Configuration", "distillTopic": "Distillation Topic", "tagLevels": "Tag Levels", "tagLevelsHelper": "Set the number of levels, maximum is {{max}}", "tagsPerLevel": "Tags Per Level", "tagsPerLevelHelper": "Number of sub-tags to generate under each parent tag, maximum is {{max}}", "questionsPerTag": "Questions Per Tag", "questionsPerTagHelper": "Number of questions to generate for each leaf tag, maximum is {{max}}", "estimationInfo": "Task Estimation Info", "estimatedTags": "Estimated Tags", "estimatedQuestions": "Estimated Questions", "currentTags": "Current Tags", "currentQuestions": "Current Questions", "newTags": "New Tags", "newQuestions": "New Questions", "startAutoDistill": "Start Auto Distillation", "autoDistillProgress": "Auto Distillation Progress", "overallProgress": "Overall Progress", "tagsProgress": "Tag Building Progress", "questionsProgress": "Question Generation Progress", "currentStage": "Current Stage", "realTimeLogs": "Real-time Logs", "waitingForLogs": "Waiting for logs...", "autoDistillStarted": "{{time}} Auto distillation task started", "autoDistillInsufficientError": "Current configuration will not produce new tags or questions, please adjust parameters", "stageInitializing": "Initializing...", "stageBuildingLevel1": "Building Level 1 Tags", "stageBuildingLevel2": "Building Level 2 Tags", "stageBuildingLevel3": "Building Level 3 Tags", "stageBuildingLevel4": "Building Level 4 Tags", "stageBuildingLevel5": "Building Level 5 Tags", "stageBuildingQuestions": "Generating Questions", "stageBuildingDatasets": "Building Datasets", "stageCompleted": "Task Completed", "datasetsProgress": "Datasets Progress", "rootTopicHelperText": "By default, the project name is used as the top-level distillation theme. If you need to change it, please go to the project settings to modify the project name.", "addChildTag": "Add Child Tag"}, "tasks": {"pending": "{{count}} tasks are processing", "completed": "tasks are completed", "title": "Task Management Center", "loading": "Loading tasks...", "empty": "No tasks found", "confirmDelete": "Are you sure you want to delete this task?", "confirmAbort": "Are you sure you want to abort this task? The task will be stopped.", "deleteSuccess": "Task deleted", "deleteFailed": "Failed to delete task", "abortSuccess": "Task aborted", "abortFailed": "Failed to abort task", "status": {"processing": "Processing", "completed": "Completed", "failed": "Failed", "aborted": "Aborted", "unknown": "Unknown"}, "types": {"text-processing": "Text Processing", "file-processing": "File Processing", "question-generation": "Question Generation", "answer-generation": "Answer Generation", "data-distillation": "Data Distillation", "dataset-import": "Dataset Import", "pdf-processing": "PDF Processing", "unknown": "Unknown Task"}, "filters": {"status": "Task Status", "type": "Task Type"}, "actions": {"refresh": "Refresh task list", "delete": "Delete task", "abort": "Abort task"}, "table": {"type": "Type", "status": "Status", "progress": "Progress", "success": "Success", "failed": "Failed", "createTime": "Created", "endTime": "Completed", "duration": "Duration", "model": "Model", "detail": "Details", "actions": "Actions"}, "duration": {"seconds": "{{seconds}}s", "minutes": "{{minutes}}m {{seconds}}s", "hours": "{{hours}}h {{minutes}}m"}, "fetchFailed": "Failed to fetch task list"}, "gaPairs": {"title": "Genre-Audience Pairs Management", "loading": "Loading GA pairs...", "addPair": "Add GA Pair", "saveChanges": "Save Changes", "saving": "Saving...", "restoreBackup": "Restore Backup", "noGaPairsTitle": "No Genre-Audience Pairs Found", "noGaPairsDescription": "Generate AI-powered Genre-Audience pairs for this file", "generateGaPairs": "Generate Genre-Audience Pairs", "generating": "Generating...", "generateMore": "Generate More Genre-Audience Pairs", "activePairs": "Active Genre-Audience Pairs ({{active}}/{{total}})", "pairNumber": "Genre-Audience Pair #{{number}}", "active": "Active", "deleteTooltip": "Delete GA Pair", "genre": "Genre", "genreDescription": "Genre Description", "audience": "Audience", "audienceDescription": "Audience Description", "addDialogTitle": "Add New Genre-Audience Pair", "genreTitle": "Genre Title", "audienceTitle": "Audience Title", "genreTitlePlaceholder": "Enter the genre title...", "genreDescPlaceholder": "Describe the genre in detail...", "audienceTitlePlaceholder": "Enter the audience title...", "audienceDescPlaceholder": "Describe the target audience in detail...", "cancel": "Cancel", "addPairButton": "Add Genre-Audience Pair", "requiredFields": "Genre Title and Audience Title are required", "restoredFromBackup": "Restored from backup", "allPairsDeleted": "All GA pairs deleted successfully", "pairsSaved": "{{count}} GA pairs saved successfully", "additionalPairsGenerated": "Successfully generated {{count}} additional Genre-Audience pairs. Total: {{total}}", "validationError": "GA pair {{number}}: Genre and Audience titles are required", "loadError": "Unable to load GA pairs: {{error}}", "generateError": "Failed to generate GA pairs", "saveError": "Failed to save GA pairs", "noActiveModel": "Please configure an AI model in settings before generating GA pairs.", "contentTooShort": "The file content is too short or not suitable for GA pair generation.", "configError": "AI model configuration error. The required dependencies may not be installed.", "serverError": "Server error ({{status}}). Please try again later.", "emptyResponse": "Empty response from generation service", "generationFailed": "Generation failed", "saveOperationFailed": "Save operation failed", "serviceNotAvailable": "GA Pairs generation service is not available. Please check your API configuration.", "requestFailed": "Request failed ({{status}}). Please try again.", "internalServerError": "Internal server error occurred.", "batchGenerate": "Batch Generate GA Pairs", "batchGenerateDescription": "Will batch generate GA pairs for {{count}} selected files. This operation may take some time.", "appendMode": "Append Mode", "appendModeDescription": "Generate additional GA pairs for files that already have GA pairs, rather than overwriting", "selectAtLeastOneFile": "Please select at least one file first", "noDefaultModel": "No default model set, please configure a model in project settings first", "incompleteModelConfig": "Model configuration is incomplete, please check model settings", "missingApiKey": "Model API key not configured, please add API key in model settings", "loadingProjectModel": "Loading project model...", "usingModel": "Using model", "startGeneration": "Start Generation", "batchGenCompleted": "Batch generation completed! Successfully generated GA pairs for {{success}}/{{total}} files.", "generationError": "Error occurred during generation: {{error}}", "fetchProjectInfoFailed": "Failed to fetch project info: {{status}}", "fetchModelConfigFailed": "Failed to fetch model config: {{status}}", "fetchProjectModelError": "Error fetching project model configuration", "batchGenerationFailed": "Batch GA pair generation failed", "batchGenerationSuccess": "Successfully generated GA pairs for {{count}} files", "selectAllFiles": "Select All", "deselectAllFiles": "Deselect All"}, "batchEdit": {"title": "Batch Edit Text Chunks", "batchEdit": "<PERSON>ch Edit", "batchEditTooltip": "<PERSON><PERSON> edit selected text chunks", "position": "Add Position", "atBeginning": "Add at Beginning", "atEnd": "Add at End", "contentToAdd": "Content to Add", "contentPlaceholder": "Enter content to add to text chunks...", "contentRequired": "Please enter content to add", "contentHelp": "This content will be added to all selected text chunks", "preview": "Preview", "allChunksSelected": "All {{count}} text chunks selected", "selectedChunks": "{{selected}} / {{total}} text chunks selected", "processing": "Processing...", "applyToChunks": "Apply to {{count}} chunks", "editSuccess": "Successfully edited {{count}} text chunks", "editFailed": "Batch edit failed", "previewNote": "The above is a preview of the first selected text chunk. All selected text chunks will undergo the same modification"}}