// 从 LLM 输出中提取 JSON
function extractJsonFromLLMOutput(output) {
  console.log('LLM 输出:', output);

  // 确保output是字符串类型
  if (typeof output !== 'string') {
    console.error('LLM输出不是字符串类型:', typeof output);
    if (output && typeof output === 'object') {
      try {
        // 如果是对象，检查是否是有效的数组
        if (Array.isArray(output)) {
          return output;
        }
        // 如果是对象，尝试直接返回
        return output;
      } catch (error) {
        console.error('无法处理非字符串类型的LLM输出:', error);
        return [];
      }
    }
    return [];
  }

  // 检查空字符串或只有空白字符的情况
  if (!output || output.trim().length === 0) {
    console.error('LLM输出为空或只包含空白字符');
    return [];
  }

  if (output.trim().startsWith('<think')) {
    output = extractAnswer(output);
  }

  // 首先尝试直接解析整个输出
  try {
    const json = JSON.parse(output);
    return json;
  } catch (error) {
    // 如果直接解析失败，尝试提取代码块中的JSON
    console.log('直接解析JSON失败，尝试提取代码块');
  }

  // 查找代码块标记
  const jsonStart = output.indexOf('```json');
  const jsonEnd = output.lastIndexOf('```');

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    // 提取代码块内容，确保去除开始的```json和结束的```
    let jsonString = output.substring(jsonStart + 7, jsonEnd).trim();

    // 处理可能存在的换行符
    if (jsonString.startsWith('\n')) {
      jsonString = jsonString.substring(1);
    }

    console.log('提取的JSON字符串:', jsonString);

    try {
      const json = JSON.parse(jsonString);
      return json;
    } catch (error) {
      console.error('解析提取的JSON时出错:', { error, jsonString });

      // 尝试修复常见的JSON格式问题
      try {
        // 替换可能导致问题的特殊字符
        const fixedJsonString = jsonString
            .replace(/[\u2018\u2019]/g, "'")
            .replace(/[\u201C\u201D]/g, '"')
            .replace(/…/g, '...');

        console.log('尝试修复后的JSON:', fixedJsonString);
        const json = JSON.parse(fixedJsonString);
        return json;
      } catch (fixError) {
        console.error('修复JSON后解析仍然失败:', fixError);
      }
    }
  } else {
    // 如果没有找到代码块标记，尝试寻找数组或对象的开始和结束
    const arrayStart = output.indexOf('[');
    const arrayEnd = output.lastIndexOf(']');
    const objectStart = output.indexOf('{');
    const objectEnd = output.lastIndexOf('}');

    if (arrayStart !== -1 && arrayEnd !== -1 && arrayEnd > arrayStart) {
      try {
        const jsonString = output.substring(arrayStart, arrayEnd + 1);
        console.log('尝试解析数组:', jsonString);
        const json = JSON.parse(jsonString);
        return json;
      } catch (error) {
        console.error('解析数组失败:', error);
      }
    } else if (objectStart !== -1 && objectEnd !== -1 && objectEnd > objectStart) {
      try {
        const jsonString = output.substring(objectStart, objectEnd + 1);
        console.log('尝试解析对象:', jsonString);
        const json = JSON.parse(jsonString);
        return json;
      } catch (error) {
        console.error('解析对象失败:', error);
      }
    }

    console.error('模型未按标准格式输出:', output);
    // 尝试最后的兜底方案：寻找任何可能的JSON结构
    const fallbackResult = tryExtractAnyJson(output);
    if (fallbackResult && Array.isArray(fallbackResult)) {
      return fallbackResult;
    }
    console.error('所有JSON提取方案都失败，返回空数组');
    return [];
  }
}

function extractThinkChain(text) {
  const startTags = ['<think>', '<thinking>'];
  const endTags = ['</think>', '</thinking>'];
  let startIndex = -1;
  let endIndex = -1;
  let usedStartTag = '';
  let usedEndTag = '';

  for (let i = 0; i < startTags.length; i++) {
    const currentStartIndex = text.indexOf(startTags[i]);
    if (currentStartIndex !== -1) {
      startIndex = currentStartIndex;
      usedStartTag = startTags[i];
      usedEndTag = endTags[i];
      break;
    }
  }

  if (startIndex === -1) {
    return '';
  }

  endIndex = text.indexOf(usedEndTag, startIndex + usedStartTag.length);

  if (endIndex === -1) {
    return '';
  }

  return text.slice(startIndex + usedStartTag.length, endIndex).trim();
}

function extractAnswer(text) {
  const startTags = ['<think>', '<thinking>'];
  const endTags = ['</think>', '</thinking>'];
  for (let i = 0; i < startTags.length; i++) {
    const start = startTags[i];
    const end = endTags[i];
    if (text.includes(start) && text.includes(end)) {
      const partsBefore = text.split(start);
      const partsAfter = partsBefore[1].split(end);
      return (partsBefore[0].trim() + ' ' + partsAfter[1].trim()).trim();
    }
  }
  return text;
}

// 兜底方案：尝试从文本中提取任何可能的JSON结构
function tryExtractAnyJson(text) {
  try {
    // 尝试使用正则表达式匹配JSON数组
    const arrayMatches = text.match(/\[[\s\S]*?\]/g);
    if (arrayMatches && arrayMatches.length > 0) {
      for (const match of arrayMatches) {
        try {
          const parsed = JSON.parse(match);
          if (Array.isArray(parsed)) {
            return parsed;
          }
        } catch (e) {
          continue;
        }
      }
    }

    // 尝试匹配JSON对象
    const objectMatches = text.match(/\{[\s\S]*?\}/g);
    if (objectMatches && objectMatches.length > 0) {
      for (const match of objectMatches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed && typeof parsed === 'object') {
            return parsed;
          }
        } catch (e) {
          continue;
        }
      }
    }

    // 如果都失败了，返回空数组
    return [];
  } catch (error) {
    console.error('兜底JSON提取失败:', error);
    return [];
  }
}

module.exports = {
  extractJsonFromLLMOutput,
  extractThinkChain,
  extractAnswer
};
