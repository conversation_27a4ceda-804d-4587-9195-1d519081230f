/**
 * 批量自动识别领域标签任务处理服务
 */

import {PrismaClient} from '@prisma/client';
import {updateTask} from './index';
import {createTag, getTags} from '@/lib/db/tags';
import {getProject} from '@/lib/db/projects';
import {updateDataset, batchUpdateDatasetLabels} from '@/lib/db/datasets';
import LLMClient from '@/lib/llm/core/index';
import {getAddLabelSimplePrompt} from '@/lib/llm/prompts/addLabelSimple';
import {extractTagFromText, hasErrorInResponse, parseLLMResponse} from '@/lib/util/llm-response-parser';
import {getActiveModel} from '@/lib/services/models';

const prisma = new PrismaClient();

/**
 * 处理批量自动识别领域标签任务
 * @param {Object} task - 任务对象
 */
export async function processBatchAutoTagTask(task) {
  try {
    console.log(`开始处理批量自动识别领域标签任务: ${task.id}`);

    // 解析任务信息
    let taskNote;
    try {
      taskNote = JSON.parse(task.note);
    } catch (error) {
      throw new Error(`任务信息解析失败: ${error.message}`);
    }

    const { projectId, datasetIds } = taskNote;

    if (!projectId || !datasetIds || !Array.isArray(datasetIds) || datasetIds.length === 0) {
      throw new Error('任务参数无效：缺少项目ID或数据集ID列表');
    }

    // 更新任务状态为处理中
    await updateTask(task.id, {
      status: 0,
      totalCount: datasetIds.length,
      completedCount: 0,
      detail: '正在初始化...'
    });

    // 获取项目的领域标签
    let projectTags = await getTags(projectId);
    if (!projectTags || projectTags.length === 0) {
      throw new Error('项目没有领域标签，无法自动识别');
    }

    // 获取项目配置
    const projectConfig = await getProject(projectId);
    let domainTreePrompt = '';
    if (projectConfig && projectConfig.domainTreePrompt) {
      domainTreePrompt = projectConfig.domainTreePrompt;
    }

    // 获取要处理的数据集
    const datasets = await prisma.datasets.findMany({
      where: {
        id: { in: datasetIds },
        projectId: projectId
      },
      select: {
        id: true,
        question: true,
        questionLabel: true
      }
    });

    if (datasets.length === 0) {
      throw new Error('未找到要处理的数据集');
    }

    // 获取项目的默认模型配置
    const modelConfig = await getActiveModel(projectId);
    if (!modelConfig) {
      throw new Error('项目未配置默认模型，无法进行自动识别');
    }

    // 准备标签列表，包含层级关系信息
    const simplifiedTags = [];

    // 递归构建标签树，保留层级关系
    function buildTagTree(tags, parentId = null, path = '') {
      const result = [];

      // 找出当前层级的标签
      const currentLevelTags = tags.filter(tag => tag.parentId === parentId);

      for (const tag of currentLevelTags) {
        // 创建当前标签对象
        const currentPath = path ? `${path} > ${tag.label}` : tag.label;
        const tagObj = {
          id: tag.id,
          name: tag.label,
          parentId: tag.parentId,
          path: currentPath
        };

        // 递归处理子标签
        const children = buildTagTree(tags, tag.id, currentPath);
        if (children.length > 0) {
          tagObj.children = children;
        }

        result.push(tagObj);

        // 同时添加到扁平列表，便于LLM处理
        simplifiedTags.push(tagObj);
      }

      return result;
    }

    // 构建标签树
    let tagTree = buildTagTree(projectTags);

    // 创建LLM客户端
    const llmClient = new LLMClient(modelConfig);

    // 处理结果统计
    const results = {
      total: datasets.length,
      success: 0,
      failed: 0,
      errors: []
    };

    // 优化后的批量处理逻辑
    const batchSize = 10; // 批量处理大小
    const pendingUpdates = []; // 待更新的数据集标签
    const tagCache = new Map(); // 标签缓存，避免重复查找
    let needsTagRefresh = false; // 是否需要刷新标签列表

    // 分批处理数据集
    for (let batchStart = 0; batchStart < datasets.length; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, datasets.length);
      const currentBatch = datasets.slice(batchStart, batchEnd);

      // 更新任务进度
      await updateTask(task.id, {
        completedCount: batchStart,
        detail: `正在处理第 ${batchStart + 1}-${batchEnd}/${datasets.length} 个数据集...`
      });

      // 并行处理当前批次的LLM请求
      const batchPromises = currentBatch.map(async (dataset, index) => {
        try {
          // 准备提示词
          const isReasoningModel = modelConfig.name && (modelConfig.name.toLowerCase().includes('o1') || modelConfig.name.toLowerCase().includes('reasoning'));
          const promptContent = getAddLabelSimplePrompt(
            JSON.stringify(tagTree, null, 2),
            dataset.question,
            domainTreePrompt,
            isReasoningModel
          );

          // 添加重试逻辑
          let retryCount = 0;
          let success = false;
          let response;

          while (retryCount < 3 && !success) {
            try {
              // 发送请求，设置较短的超时时间
              response = await Promise.race([
                llmClient.getResponse(promptContent),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('请求超时')), 30000) // 30秒超时
                )
              ]);
              success = true;
            } catch (retryError) {
              retryCount++;

              // 如果是最后一次重试失败，记录错误并跳过
              if (retryCount >= 3) {
                console.error(`跳过问题"${dataset.question.substring(0, 20)}..."，无法自动识别标签`);
                return {
                  datasetId: dataset.id,
                  success: false,
                  error: 'LLM调用失败'
                };
              }

              // 等待一段时间再重试
              await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000)); // 随机延迟避免并发冲突
            }
          }

          // 如果成功获取响应
          if (success && response) {
            let label = '';
            let reasoning = '';

            // 检查响应是否包含错误信息
            if (hasErrorInResponse(response)) {
              console.warn(`自动识别标签失败，使用默认标签: ${response}`);
              label = '未分类';
            } else {
              // 尝试解析JSON响应
              const parseResult = parseLLMResponse(response);

              if (parseResult.success) {
                const parentTag = parseResult.parentTag;
                const childTag = parseResult.childTag;
                reasoning = parseResult.reasoning;

                // 使用子标签作为最终标签
                label = childTag || '未分类';
              } else {
                console.warn('JSON解析失败，尝试从文本中提取标签:', parseResult.error);

                // 如果JSON解析失败，尝试从文本中提取
                label = extractTagFromText(response);
              }
            }

            return {
              datasetId: dataset.id,
              success: true,
              label: label,
              reasoning: reasoning
            };
          } else {
            return {
              datasetId: dataset.id,
              success: false,
              error: 'LLM调用失败',
              label: '未分类'
            };
          }
        } catch (error) {
          console.error(`处理数据集 ${dataset.id} 失败:`, error);
          return {
            datasetId: dataset.id,
            success: false,
            error: error.message,
            label: '未分类'
          };
        }
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);

      // 处理批次结果，准备标签更新
      for (const result of batchResults) {
        if (result.label) {
          try {
            // 使用缓存查找或创建标签
            let matchedLabel;
            if (tagCache.has(result.label)) {
              matchedLabel = tagCache.get(result.label);
            } else {
              matchedLabel = await findOrCreateTag(projectId, result.label);
              tagCache.set(result.label, matchedLabel);

              // 如果创建了新标签，标记需要刷新
              if (matchedLabel !== result.label) {
                needsTagRefresh = true;
              }
            }

            // 添加到待更新列表
            pendingUpdates.push({
              id: result.datasetId,
              questionLabel: matchedLabel
            });

            if (result.success) {
              results.success++;
            } else {
              results.failed++;
              results.errors.push(`数据集 ${result.datasetId}: ${result.error}`);
            }
          } catch (error) {
            console.error(`处理标签失败: ${error.message}`);
            results.failed++;
            results.errors.push(`数据集 ${result.datasetId}: 处理标签失败 - ${error.message}`);
          }
        } else {
          results.failed++;
          results.errors.push(`数据集 ${result.datasetId}: 无法获取有效标签`);
        }
      }

      // 如果需要刷新标签列表，在批次处理完成后刷新一次
      if (needsTagRefresh) {
        try {
          console.log('刷新标签列表...');
          projectTags = await getTags(projectId);
          tagTree = buildTagTree(projectTags);
          simplifiedTags.length = 0;
          for (const tag of projectTags) {
            const tagObj = {
              id: tag.id,
              name: tag.label,
              parentId: tag.parentId
            };
            simplifiedTags.push(tagObj);
          }
          needsTagRefresh = false;
        } catch (refreshError) {
          console.error(`刷新标签列表失败: ${refreshError.message}`);
        }
      }
    }

    // 批量更新数据集标签
    if (pendingUpdates.length > 0) {
      try {
        await batchUpdateDatasetLabels(pendingUpdates);
      } catch (error) {
        console.error(`批量更新数据集标签失败: ${error.message}`);
        // 如果批量更新失败，尝试逐个更新
        for (const update of pendingUpdates) {
          try {
            const updateResult = await updateDatasetLabel(update.id, update.questionLabel);
            if (updateResult) {
              results.success++;
            } else {
              results.failed++;
              results.errors.push(`数据集 ${update.id}: 更新标签失败`);
            }
          } catch (individualError) {
            console.error(`更新数据集 ${update.id} 标签失败: ${individualError.message}`);
            results.failed++;
            results.errors.push(`数据集 ${update.id}: 更新标签失败 - ${individualError.message}`);
          }
        }
      }
    }

    // 任务完成
    const finalDetail = `处理完成：成功 ${results.success}/${results.total}，失败 ${results.failed}/${results.total}`;

    await updateTask(task.id, {
      status: 1, // 完成
      completedCount: datasets.length,
      detail: finalDetail,
      note: JSON.stringify({ ...taskNote, results }),
      endTime: new Date()
    });

    console.log(`批量自动识别领域标签任务完成: ${task.id}`);
    console.log(`处理结果: ${finalDetail}`);

  } catch (error) {
    console.error(`批量自动识别领域标签任务失败: ${task.id}`, error);

    await updateTask(task.id, {
      status: 2, // 失败
      detail: `任务失败: ${error.message}`,
      endTime: new Date()
    });

    throw error;
  }
}

/**
 * 更新数据集标签
 * @param {string} datasetId - 数据集ID
 * @param {string} label - 标签名称
 */
async function updateDatasetLabel(datasetId, label) {
  try {
    // 确保label是标签名称而不是ID
    if (!label || typeof label !== 'string') {
      console.error(`无效的标签值: ${label}`);
      return false;
    }

    // 首先获取数据集信息以获取关联的问题ID
    const db = new PrismaClient();
    const dataset = await db.datasets.findUnique({
      where: { id: datasetId },
      select: { questionId: true }
    });
    await db.$disconnect();

    // 使用数据库更新函数直接更新数据集
    await updateDataset({
      id: datasetId,
      questionLabel: label
    });

    // 同时更新关联的真实问题记录的标签
    if (dataset && dataset.questionId) {
      try {
        // 先检查问题记录是否存在
        const db2 = new PrismaClient();
        const existingQuestion = await db2.questions.findUnique({
          where: { id: dataset.questionId },
          select: { id: true }
        });
        await db2.$disconnect();

        if (existingQuestion) {
          const { updateQuestion } = await import('@/lib/db/questions');
          await updateQuestion(dataset.questionId, {
            label: label
          });
        }
      } catch (questionUpdateError) {
        // 不抛出错误，继续执行，因为数据集标签已经更新成功
      }
    }
    return true;
  } catch (error) {
    // 不抛出错误，避免中断整个批量处理过程
    return false;
  }
}

/**
 * 查找或创建标签（复用dataset-import.js中的逻辑）
 */
async function findOrCreateTag(projectId, labelName, suggestNewSubclass = null) {
  try {
    // 获取项目的所有标签
    const allTags = await getTags(projectId);

    // 去除前后空格
    const trimmedLabelName = labelName.trim();

    // 1. 首先进行精确匹配（完全相同的标签名称）
    const exactMatch = allTags.find(tag => tag.label === trimmedLabelName);
    if (exactMatch) {
      console.log(`找到完全匹配的标签: ${exactMatch.label}`);
      return exactMatch.label;
    }

    // 2. 如果没有精确匹配，进行大小写不敏感的匹配
    const caseInsensitiveMatch = allTags.find(tag =>
      tag.label.toLowerCase() === trimmedLabelName.toLowerCase()
    );
    if (caseInsensitiveMatch) {
      console.log(`找到大小写不敏感匹配的标签: ${caseInsensitiveMatch.label}`);
      return caseInsensitiveMatch.label;
    }

    // 3. 检查是否是子标签格式（如"1.1 社交互动"）
    const childTagMatch = trimmedLabelName.match(/^(\d+)\.(\d+)\s+(.+)$/);
    if (childTagMatch) {
      const parentNum = childTagMatch[1];
      const childNum = childTagMatch[2];
      const childName = childTagMatch[3];

      console.log(`检测到子标签格式: ${trimmedLabelName}，父标签序号: ${parentNum}，子标签序号: ${childNum}，标签名: ${childName}`);

      // 查找对应的父标签
      const parentTagName = await findOrCreateParentTag(projectId, parentNum);
      if (parentTagName) {
        console.log(`找到现有父标签: ${parentTagName}`);
        // 创建或查找子标签
        const childTagName = await findOrCreateChildTag(projectId, parentTagName, childName, childNum);
        console.log(`创建新的子标签: ${childTagName}，父标签: ${parentTagName}`);
        return childTagName;
      }
    }

    // 4. 如果都没有匹配，智能创建新标签
    console.log(`未找到匹配的标签，将智能创建新标签: ${trimmedLabelName}`);

    // 检查是否是一级标签（父级标签）
    function isParentTag(name) {
      return name.includes('领域') || /^[一二三四五六七八九十]+、/.test(name);
    }

    // 智能处理新标签创建
    try {
      // 检查是否是建议的子标签格式（如"7.1 无意义表达"）
      const suggestedChildMatch = trimmedLabelName.match(/^(\d+)\.(\d+)\s+(.+)$/);
      if (suggestedChildMatch) {
        const parentNum = suggestedChildMatch[1];
        const childNum = suggestedChildMatch[2];
        const childName = suggestedChildMatch[3];

        // 查找或创建对应的父标签
        const parentTagName = await findOrCreateParentTag(projectId, parentNum);
        if (parentTagName) {
          // 创建子标签
          return await findOrCreateChildTag(projectId, parentTagName, childName, childNum);
        }
      }

      // 如果不是特殊格式，直接创建为独立标签
      await createTag(projectId, trimmedLabelName, null);
      return trimmedLabelName;
    } catch (error) {
      console.error(`创建标签失败: ${error.message}`);
      // 创建失败时，尝试创建到"其他"分类下
      try {
        const otherParentName = await findOrCreateParentTag(projectId, '7');
        return await findOrCreateChildTag(projectId, otherParentName, '其他', '9');
      } catch (fallbackError) {
        console.error(`备用标签创建也失败: ${fallbackError.message}`);
        return '未分类';
      }
    }
  } catch (error) {
    console.error(`查找或创建标签失败: ${error.message}`);
    // 出错时返回原始标签名称
    return labelName;
  }
}

/**
 * 查找或创建父标签（复用dataset-import.js中的逻辑）
 */
async function findOrCreateParentTag(projectId, parentNum) {
  try {
    const allTags = await getTags(projectId);

    // 查找现有的父标签（一级标签）
    const existingParent = allTags.find(tag => {
      if (tag.parentId) return false; // 只查找一级标签

      // 匹配中文数字格式
      const chineseMatch = tag.label.match(/^([一二三四五六七八九十]+)、/);
      if (chineseMatch) {
        const chineseToArabic = {
          '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
          '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
        };
        return chineseToArabic[chineseMatch[1]] === parentNum;
      }

      // 匹配阿拉伯数字格式
      const arabicMatch = tag.label.match(/^(\d+)[、\.]/);
      if (arabicMatch) {
        return arabicMatch[1] === parentNum;
      }

      return false;
    });

    if (existingParent) {
      console.log(`找到现有父标签: ${existingParent.label}`);
      return existingParent.label;
    }

    // 如果没有找到，创建新的父标签
    const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    const parentIndex = parseInt(parentNum) - 1;
    let parentPrefix;

    if (parentIndex >= 0 && parentIndex < chineseNumbers.length) {
      parentPrefix = `${chineseNumbers[parentIndex]}、`;
    } else {
      parentPrefix = `${parentNum}、`;
    }

    // 根据序号确定领域名称
    const domainNames = {
      '1': '社交互动',
      '2': '日常生活',
      '3': '学习发展',
      '4': '职业工作',
      '5': '休闲娱乐',
      '6': '健康医疗',
      '7': '其他'
    };

    const domainName = domainNames[parentNum] || '其他';
    const newParentName = `${parentPrefix}${domainName}`;

    // 创建父标签
    await createTag(projectId, newParentName, null);
    console.log(`创建新的父标签: ${newParentName}`);

    return newParentName;
  } catch (error) {
    console.error(`查找或创建父标签失败: ${error.message}`);
    return '其他';
  }
}

/**
 * 查找或创建子标签（复用dataset-import.js中的逻辑）
 */
async function findOrCreateChildTag(projectId, parentTagName, childTagName, childNum) {
  try {
    const allTags = await getTags(projectId);

    // 找到父标签
    const parentTag = allTags.find(tag => tag.label === parentTagName && !tag.parentId);
    if (!parentTag) {
      console.error(`未找到父标签: ${parentTagName}`);
      return childTagName;
    }

    // 清理子标签名称，去除可能的数字前缀（如"1.1 社交互动"中的"1.1 "）
    const cleanChildName = childTagName.replace(/^\d+\.\d+\s+/, '').trim();

    // 查找现有的子标签
    const existingChild = allTags.find(tag => {
      if (tag.parentId !== parentTag.id) return false;

      // 精确匹配标签名（不带序号）
      if (tag.label === cleanChildName) return true;

      // 匹配带序号的格式，提取标签名进行比较
      const match = tag.label.match(/^\d+\.\d+\s+(.+)$/);
      if (match && match[1] === cleanChildName) return true;

      return false;
    });

    if (existingChild) {
      console.log(`找到现有子标签: ${existingChild.label}`);
      return existingChild.label;
    }

    // 获取父标签的阿拉伯数字序号
    const parentNum = parentTagName.match(/^([一二三四五六七八九十]+|\d+)/)?.[1];
    let parentNumArabic = parentNum;

    // 转换中文数字为阿拉伯数字
    if (parentNum) {
      const chineseToArabic = {
        '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
        '六': '6', '七': '7', '八': '8', '九': '9', '十': '10'
      };
      parentNumArabic = chineseToArabic[parentNum] || parentNum;
    }

    // 查找该父标签下已存在的子标签，确定下一个可用的子序号
    const existingChildTags = allTags.filter(tag => tag.parentId === parentTag.id);
    let maxChildNum = 0;

    existingChildTags.forEach(tag => {
      const match = tag.label.match(/^\d+\.(\d+)\s+/);
      if (match) {
        const childSeqNum = parseInt(match[1], 10);
        if (!isNaN(childSeqNum) && childSeqNum > maxChildNum) {
          maxChildNum = childSeqNum;
        }
      }
    });

    // 使用下一个可用的子序号
    const nextChildNum = maxChildNum + 1;
    const newChildName = `${parentNumArabic}.${nextChildNum} ${cleanChildName}`;

    // 创建子标签
    await createTag(projectId, newChildName, parentTag.id);
    console.log(`创建新的子标签: ${newChildName}，父标签: ${parentTagName}，使用序号: ${nextChildNum}`);

    return newChildName;
  } catch (error) {
    console.error(`查找或创建子标签失败: ${error.message}`);
    return childTagName;
  }
}
